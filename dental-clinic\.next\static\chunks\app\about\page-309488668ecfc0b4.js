(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[220],{4186:(e,t,i)=>{"use strict";i.d(t,{A:()=>a});let a=(0,i(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4516:(e,t,i)=>{"use strict";i.d(t,{A:()=>a});let a=(0,i(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5525:(e,t,i)=>{"use strict";i.d(t,{A:()=>a});let a=(0,i(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},6928:(e,t,i)=>{"use strict";i.d(t,{default:()=>m});var a=i(5155),s=i(2605);let r=(0,i(9946).A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]);var l=i(4186),n=i(4090),c=i(6695),d=i(6126),o=i(8720);function m(){return(0,a.jsxs)(n.w,{background:"gray",children:[(0,a.jsx)(n.X,{subtitle:"Meet Our Team",title:"Expert Dental Professionals",description:"Our experienced team of dental professionals is dedicated to providing you with the highest quality care in a comfortable and welcoming environment."}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:o.x1.map((e,t)=>(0,a.jsx)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},children:(0,a.jsx)(c.Zp,{className:"h-full hover:shadow-lg transition-shadow duration-300",children:(0,a.jsxs)(c.Wu,{className:"p-6",children:[(0,a.jsx)("div",{className:"w-full h-64 bg-gray-200 rounded-xl mb-6 overflow-hidden",children:(0,a.jsx)("img",{src:e.image,alt:e.name,className:"w-full h-full object-cover",onError:e=>{e.target.src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=400&h=400&fit=crop&crop=face"}})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-1",children:e.name}),(0,a.jsx)("p",{className:"text-primary font-semibold mb-2",children:e.role})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)(r,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:e.qualification})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[e.experience," of experience"]})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:e.bio}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-900 mb-2 text-sm",children:"Specialties:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.specialties.map((e,t)=>(0,a.jsx)(d.E,{variant:"secondary",className:"text-xs",children:e},t))})]})]})]})})},e.id))}),(0,a.jsx)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mt-16",children:(0,a.jsx)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:"3"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Specialist Doctors"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:"25+"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Years Combined Experience"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:"5000+"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Patients Treated"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:"98%"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Patient Satisfaction"})]})]})})})]})}},7230:(e,t,i)=>{"use strict";i.d(t,{default:()=>n});var a=i(5155),s=i(2605),r=i(4090),l=i(8720);function n(){return(0,a.jsx)(r.w,{background:"gray",className:"pt-32 pb-20",children:(0,a.jsxs)("div",{className:"text-center max-w-4xl mx-auto",children:[(0,a.jsxs)(s.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,a.jsx)("p",{className:"text-primary font-semibold text-sm uppercase tracking-wide mb-4",children:"About Our Clinic"}),(0,a.jsxs)("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6",children:["Caring for Smiles Since"," ",(0,a.jsx)("span",{className:"text-primary",children:l.x0.established})]}),(0,a.jsx)("p",{className:"text-xl text-gray-600 leading-relaxed mb-8",children:l.x0.mission})]}),(0,a.jsxs)(s.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"relative w-full h-96 rounded-2xl overflow-hidden shadow-2xl",children:[(0,a.jsx)("img",{src:"https://images.unsplash.com/photo-**********-2a8555f1a136?w=800&h=400&fit=crop&crop=center",alt:"Our dental clinic team",className:"w-full h-full object-cover"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"}),(0,a.jsxs)("div",{className:"absolute bottom-6 left-6 text-white",children:[(0,a.jsx)("p",{className:"text-lg font-semibold",children:"Our Professional Team"}),(0,a.jsx)("p",{className:"text-sm opacity-90",children:"Dedicated to your oral health"})]})]})]})})}},7680:(e,t,i)=>{"use strict";i.d(t,{default:()=>u});var a=i(5155),s=i(2605),r=i(6874),l=i.n(r),n=i(1976),c=i(7580),d=i(5525),o=i(9037),m=i(2138),x=i(4090),h=i(285),p=i(6695);let y=[{icon:n.A,title:"Community Care",description:"We are deeply committed to the health and wellbeing of the Biratnagar community, providing accessible dental care for all."},{icon:c.A,title:"Local Trust",description:"Built strong relationships with local families over the years, becoming their trusted partner in oral health."},{icon:d.A,title:"Quality Assurance",description:"Maintaining the highest standards of care and safety, ensuring every patient receives exceptional treatment."},{icon:o.A,title:"Professional Excellence",description:"Recognized for our commitment to continuous learning and adopting the latest dental technologies and techniques."}],g=[{title:"Free Dental Camps",description:"Regular free dental checkup camps in schools and community centers",impact:"500+ people served annually"},{title:"Oral Health Education",description:"Educational programs about dental hygiene in local schools",impact:"20+ schools reached"},{title:"Emergency Care",description:"24/7 emergency dental services for urgent cases",impact:"Always available for emergencies"}];function u(){return(0,a.jsxs)(x.w,{background:"white",children:[(0,a.jsx)(x.X,{subtitle:"Community Impact",title:"Trusted by Biratnagar",description:"We take pride in being an integral part of the Biratnagar community, contributing to the oral health and wellbeing of our neighbors."}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16",children:y.map((e,t)=>(0,a.jsx)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},children:(0,a.jsx)(p.Zp,{className:"h-full text-center hover:shadow-lg transition-shadow duration-300",children:(0,a.jsxs)(p.Wu,{className:"p-6",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(e.icon,{className:"h-8 w-8 text-primary"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:e.title}),(0,a.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:e.description})]})})},e.title))}),(0,a.jsxs)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"bg-gradient-to-r from-primary/5 to-accent/5 rounded-2xl p-8 border border-primary/10 mb-16",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Our Community Initiatives"}),(0,a.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Beyond providing excellent dental care, we actively contribute to the health and education of our community through various initiatives."})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:g.map((e,t)=>(0,a.jsxs)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:"bg-white rounded-xl p-6 shadow-sm",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-3",children:e.title}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4 leading-relaxed",children:e.description}),(0,a.jsx)("div",{className:"text-primary font-semibold text-sm",children:e.impact})]},e.title))})]}),(0,a.jsx)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center",children:(0,a.jsxs)("div",{className:"bg-gray-50 rounded-2xl p-8",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Join Our Dental Family"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Experience the care and trust that thousands of Biratnagar families have come to rely on. Schedule your appointment today and become part of our dental family."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)(h.$,{asChild:!0,size:"lg",children:(0,a.jsx)(l(),{href:"/contact",children:"Schedule Appointment"})}),(0,a.jsx)(h.$,{asChild:!0,variant:"outline",size:"lg",children:(0,a.jsxs)(l(),{href:"/services",children:["Explore Our Services",(0,a.jsx)(m.A,{className:"ml-2 h-4 w-4"})]})})]})]})})]})}},8416:(e,t,i)=>{Promise.resolve().then(i.bind(i,7230)),Promise.resolve().then(i.bind(i,8962)),Promise.resolve().then(i.bind(i,7680)),Promise.resolve().then(i.bind(i,6928))},8962:(e,t,i)=>{"use strict";i.d(t,{default:()=>x});var a=i(5155),s=i(2605),r=i(9074),l=i(7580),n=i(9037),c=i(4516),d=i(4090),o=i(8720);let m=[{year:"2015",title:"Clinic Founded",description:"Dr. Rajesh Sharma established Dental Care Clinic Biratnagar with a vision to provide world-class dental care to the local community.",icon:r.A},{year:"2017",title:"Team Expansion",description:"Added specialized dentists including orthodontist Dr. Priya Adhikari and endodontist Dr. Amit Thapa to our growing team.",icon:l.A},{year:"2019",title:"Modern Equipment",description:"Invested in state-of-the-art dental equipment including digital X-ray machines and advanced sterilization systems.",icon:n.A},{year:"2024",title:"Community Leader",description:"Recognized as the leading dental clinic in Biratnagar, having served over 5,000 patients with exceptional care.",icon:c.A}];function x(){return(0,a.jsx)(d.w,{background:"white",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center",children:[(0,a.jsxs)(s.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-primary font-semibold text-sm uppercase tracking-wide mb-4",children:"Our Story"}),(0,a.jsxs)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-6",children:["A Journey of"," ",(0,a.jsx)("span",{className:"text-primary",children:"Excellence"})]}),(0,a.jsxs)("div",{className:"space-y-4 text-gray-600 leading-relaxed",children:[(0,a.jsx)("p",{children:o.x0.story}),(0,a.jsx)("p",{children:"What started as a small practice has grown into Biratnagar's most trusted dental clinic. Our commitment to excellence, combined with the latest dental technology and a compassionate approach, has made us the preferred choice for families throughout the region."}),(0,a.jsx)("p",{children:"We believe that everyone deserves access to quality dental care, which is why we've made it our mission to provide comprehensive services at affordable prices, without compromising on quality or comfort."})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-primary/5 p-6 rounded-xl",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Our Mission"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"To provide exceptional dental care with compassion and integrity."})]}),(0,a.jsxs)("div",{className:"bg-accent/5 p-6 rounded-xl",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Our Vision"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"To be the leading dental clinic in Nepal, setting standards for quality care."})]})]})]}),(0,a.jsxs)(s.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Our Journey"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Key milestones in our clinic's history"})]}),(0,a.jsx)("div",{className:"space-y-6",children:m.map((e,t)=>(0,a.jsxs)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-12 h-12 bg-primary rounded-full flex items-center justify-center",children:(0,a.jsx)(e.icon,{className:"h-6 w-6 text-white"})})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("span",{className:"text-lg font-bold text-primary",children:e.year}),(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:e.title})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:e.description})]})]},e.year))})]})]})})}},9074:(e,t,i)=>{"use strict";i.d(t,{A:()=>a});let a=(0,i(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])}},e=>{e.O(0,[939,874,343,441,964,358],()=>e(e.s=8416)),_N_E=e.O()}]);