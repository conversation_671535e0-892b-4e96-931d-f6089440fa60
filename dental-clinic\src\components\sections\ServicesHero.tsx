'use client';

import { motion } from 'framer-motion';
import { <PERSON>, Shield, Heart, Star } from 'lucide-react';
import { Section } from '@/components/ui/section';

const highlights = [
  {
    icon: Smile,
    title: 'Comprehensive Care',
    description: 'Complete range of dental services'
  },
  {
    icon: Shield,
    title: 'Advanced Technology',
    description: 'State-of-the-art equipment'
  },
  {
    icon: Heart,
    title: 'Gentle Approach',
    description: 'Comfortable, pain-free treatments'
  },
  {
    icon: Star,
    title: 'Expert Team',
    description: 'Experienced dental professionals'
  }
];

export default function ServicesHero() {
  return (
    <Section background="gray" className="pt-32 pb-20">
      <div className="text-center max-w-4xl mx-auto mb-16">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <p className="text-primary font-semibold text-sm uppercase tracking-wide mb-4">
            Our Services
          </p>
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Complete{' '}
            <span className="text-primary">Dental Care</span>{' '}
            Solutions
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed">
            From routine cleanings to advanced procedures, we offer comprehensive dental services 
            to keep your smile healthy, beautiful, and confident.
          </p>
        </motion.div>
      </div>

      {/* Service Highlights */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {highlights.map((highlight, index) => (
          <motion.div
            key={highlight.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center hover:shadow-md transition-shadow duration-300"
          >
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <highlight.icon className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {highlight.title}
            </h3>
            <p className="text-gray-600 text-sm">
              {highlight.description}
            </p>
          </motion.div>
        ))}
      </div>
    </Section>
  );
}
