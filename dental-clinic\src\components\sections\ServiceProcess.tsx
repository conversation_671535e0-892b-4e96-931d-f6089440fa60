'use client';

import { motion } from 'framer-motion';
import { Calendar, Search, Wrench, CheckCircle } from 'lucide-react';
import { Section, SectionHeader } from '@/components/ui/section';

const processSteps = [
  {
    icon: Calendar,
    title: 'Book Appointment',
    description: 'Schedule your visit through phone, email, or our online booking system. We offer flexible timing to suit your schedule.',
    step: '01'
  },
  {
    icon: Search,
    title: 'Comprehensive Examination',
    description: 'Our dentist will conduct a thorough examination, including X-rays if needed, to assess your oral health.',
    step: '02'
  },
  {
    icon: Wrench,
    title: 'Treatment Planning',
    description: 'We discuss your treatment options, explain procedures, and create a personalized treatment plan that fits your needs.',
    step: '03'
  },
  {
    icon: CheckCircle,
    title: 'Quality Treatment',
    description: 'Receive expert dental care using advanced techniques and equipment, ensuring comfortable and effective treatment.',
    step: '04'
  }
];

export default function ServiceProcess() {
  return (
    <Section background="gray">
      <SectionHeader
        subtitle="How It Works"
        title="Our Treatment Process"
        description="We follow a systematic approach to ensure you receive the best possible dental care from consultation to completion."
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {processSteps.map((step, index) => (
          <motion.div
            key={step.title}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            viewport={{ once: true }}
            className="relative"
          >
            {/* Connector Line */}
            {index < processSteps.length - 1 && (
              <div className="hidden lg:block absolute top-16 left-full w-full h-0.5 bg-primary/20 z-0" />
            )}
            
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 relative z-10 text-center hover:shadow-md transition-shadow duration-300">
              {/* Step Number */}
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold">
                  {step.step}
                </div>
              </div>

              {/* Icon */}
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4 mt-4">
                <step.icon className="h-8 w-8 text-primary" />
              </div>

              {/* Content */}
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {step.title}
              </h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                {step.description}
              </p>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Process Benefits */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="mt-16"
      >
        <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Why Choose Our Process?
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Our systematic approach ensures that every patient receives personalized, 
              high-quality care tailored to their specific needs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Personalized Care</h4>
              <p className="text-sm text-gray-600">Every treatment plan is customized to your unique needs</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <CheckCircle className="h-6 w-6 text-blue-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Transparent Communication</h4>
              <p className="text-sm text-gray-600">Clear explanation of procedures and costs upfront</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <CheckCircle className="h-6 w-6 text-purple-600" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Quality Assurance</h4>
              <p className="text-sm text-gray-600">Follow-up care to ensure optimal treatment outcomes</p>
            </div>
          </div>
        </div>
      </motion.div>
    </Section>
  );
}
