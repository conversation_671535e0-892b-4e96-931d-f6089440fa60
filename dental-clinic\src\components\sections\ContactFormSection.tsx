'use client';

import { motion } from 'framer-motion';
import { MessageSquare, Calendar, Phone, Mail } from 'lucide-react';
import { Section, SectionHeader } from '@/components/ui/section';
import ContactForm from '@/components/ui/contact-form';

const contactMethods = [
  {
    icon: MessageSquare,
    title: 'Online Form',
    description: 'Fill out our contact form and we\'ll get back to you within 24 hours',
    highlight: 'Most Popular'
  },
  {
    icon: Phone,
    title: 'Phone Call',
    description: 'Speak directly with our staff to schedule your appointment',
    highlight: 'Immediate Response'
  },
  {
    icon: Mail,
    title: 'Email',
    description: 'Send us detailed questions about our services or your dental needs',
    highlight: 'Detailed Inquiries'
  },
  {
    icon: Calendar,
    title: 'Walk-in',
    description: 'Visit our clinic during business hours for immediate assistance',
    highlight: 'Emergency Cases'
  }
];

export default function ContactFormSection() {
  return (
    <Section background="gray">
      <SectionHeader
        subtitle="Get In Touch"
        title="Send Us a Message"
        description="Choose the best way to contact us. We're here to answer your questions and help you schedule your dental care."
      />

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Contact Methods */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="space-y-8"
        >
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-6">
              Ways to Contact Us
            </h3>
            <p className="text-gray-600 mb-8">
              We offer multiple convenient ways to get in touch with our dental team. 
              Choose the method that works best for you.
            </p>
          </div>

          <div className="space-y-6">
            {contactMethods.map((method, index) => (
              <motion.div
                key={method.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300"
              >
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                    <method.icon className="h-6 w-6 text-primary" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="text-lg font-semibold text-gray-900">
                        {method.title}
                      </h4>
                      <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                        {method.highlight}
                      </span>
                    </div>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {method.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Response Time Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="bg-gradient-to-r from-primary/5 to-accent/5 rounded-xl p-6 border border-primary/10"
          >
            <h4 className="font-semibold text-gray-900 mb-3">Response Times</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Online Form:</span>
                <span className="font-semibold text-gray-900">Within 24 hours</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Phone Calls:</span>
                <span className="font-semibold text-gray-900">Immediate</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Email:</span>
                <span className="font-semibold text-gray-900">Within 24 hours</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Emergency:</span>
                <span className="font-semibold text-red-600">24/7 Available</span>
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* Contact Form */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <ContactForm title="Send us a Message" showCard={true} />
        </motion.div>
      </div>
    </Section>
  );
}
