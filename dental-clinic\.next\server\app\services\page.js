(()=>{var a={};a.id=763,a.ids=[763],a.modules={43:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\dental\\\\dental-clinic\\\\src\\\\components\\\\sections\\\\ServicesHero.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\components\\sections\\ServicesHero.tsx","default")},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3171:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\dental\\\\dental-clinic\\\\src\\\\components\\\\sections\\\\ServiceProcess.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\components\\sections\\ServiceProcess.tsx","default")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6543:(a,b,c)=>{"use strict";c.d(b,{A:()=>h});var d=c(60687),e=c(51743),f=c(44493),g=c(96834);function h({service:a,index:b=0,showPrice:c=!1,showFullDescription:h=!1}){return(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*b},children:(0,d.jsxs)(f.Zp,{className:"h-full hover:shadow-lg transition-shadow duration-300",children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsx)("div",{className:"w-full h-48 bg-gray-200 rounded-lg mb-4 overflow-hidden",children:(0,d.jsx)("img",{src:a.image,alt:a.name,className:"w-full h-full object-cover",onError:a=>{a.target.src="https://images.unsplash.com/photo-1606811841689-23dfddce3e95?w=400&h=300&fit=crop&crop=center"}})}),(0,d.jsx)(f.ZB,{className:"text-xl font-bold text-gray-900",children:a.name}),c&&a.price&&(0,d.jsx)(g.E,{variant:"secondary",className:"w-fit",children:a.price})]}),(0,d.jsxs)(f.Wu,{children:[(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:a.description}),h&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Benefits:"}),(0,d.jsx)("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-600",children:a.benefits.map((a,b)=>(0,d.jsx)("li",{children:a},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Procedure:"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:a.procedure})]})]})]})]})})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31331:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(37413),e=c(43),f=c(65979),g=c(3171),h=c(84677);let i={title:"Dental Services | Dental Care Clinic Biratnagar - Complete Oral Care",description:"Comprehensive dental services in Biratnagar including teeth cleaning, whitening, orthodontics, implants, root canal treatment, and cosmetic dentistry.",keywords:"dental services Biratnagar, teeth cleaning, teeth whitening, orthodontics, dental implants, root canal, cosmetic dentistry"};function j(){return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(e.default,{}),(0,d.jsx)(f.default,{}),(0,d.jsx)(g.default,{}),(0,d.jsx)(h.default,{})]})}},33873:a=>{"use strict";a.exports=require("path")},37561:(a,b,c)=>{Promise.resolve().then(c.bind(c,3171)),Promise.resolve().then(c.bind(c,84677)),Promise.resolve().then(c.bind(c,65979)),Promise.resolve().then(c.bind(c,43))},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44493:(a,b,c)=>{"use strict";c.d(b,{Wu:()=>i,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},61013:(a,b,c)=>{"use strict";c.d(b,{default:()=>i});var d=c(60687),e=c(51743),f=c(71134),g=c(6543),h=c(83446);function i(){return(0,d.jsxs)(f.w,{background:"white",children:[(0,d.jsx)(f.X,{subtitle:"What We Offer",title:"Our Dental Services",description:"We provide a comprehensive range of dental services using the latest technology and techniques to ensure the best possible outcomes for our patients."}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:h.$p.map((a,b)=>(0,d.jsx)(g.A,{service:a,index:b,showPrice:!0,showFullDescription:!0},a.id))}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mt-16 text-center",children:(0,d.jsxs)("div",{className:"bg-gradient-to-r from-primary/5 to-accent/5 rounded-2xl p-8 border border-primary/10",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Don't See What You Need?"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"We offer many additional specialized dental services not listed here. Contact us to discuss your specific dental needs and we'll be happy to help."}),(0,d.jsxs)("div",{className:"flex flex-wrap justify-center gap-3 text-sm",children:[(0,d.jsx)("span",{className:"bg-white px-4 py-2 rounded-full text-gray-700",children:"Pediatric Dentistry"}),(0,d.jsx)("span",{className:"bg-white px-4 py-2 rounded-full text-gray-700",children:"Oral Surgery"}),(0,d.jsx)("span",{className:"bg-white px-4 py-2 rounded-full text-gray-700",children:"Periodontics"}),(0,d.jsx)("span",{className:"bg-white px-4 py-2 rounded-full text-gray-700",children:"Prosthodontics"}),(0,d.jsx)("span",{className:"bg-white px-4 py-2 rounded-full text-gray-700",children:"Emergency Care"}),(0,d.jsx)("span",{className:"bg-white px-4 py-2 rounded-full text-gray-700",children:"Dental Crowns"})]})]})})]})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63836:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["services",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,31331)),"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\app\\services\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,92302)),"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\app\\services\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/services/page",pathname:"/services",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/services/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},64398:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},65979:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\dental\\\\dental-clinic\\\\src\\\\components\\\\sections\\\\ServicesGrid.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\components\\sections\\ServicesGrid.tsx","default")},67760:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},71134:(a,b,c)=>{"use strict";c.d(b,{X:()=>g,w:()=>f});var d=c(60687),e=c(4780);function f({children:a,className:b,id:c,background:f="white"}){return(0,d.jsx)("section",{id:c,className:(0,e.cn)("py-16 sm:py-20",{white:"bg-white",gray:"bg-gray-50",primary:"bg-primary text-primary-foreground"}[f],b),children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a})})}function g({title:a,subtitle:b,description:c,centered:f=!0,className:g}){return(0,d.jsxs)("div",{className:(0,e.cn)("mb-12",f&&"text-center",g),children:[b&&(0,d.jsx)("p",{className:"text-primary font-semibold text-sm uppercase tracking-wide mb-2",children:b}),(0,d.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:a}),c&&(0,d.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:c})]})}},74009:(a,b,c)=>{Promise.resolve().then(c.bind(c,92360)),Promise.resolve().then(c.bind(c,79882)),Promise.resolve().then(c.bind(c,61013)),Promise.resolve().then(c.bind(c,75859))},75859:(a,b,c)=>{"use strict";c.d(b,{default:()=>l});var d=c(60687),e=c(51743);let f=(0,c(62688).A)("smile",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 14s1.5 2 4 2 4-2 4-2",key:"1y1vjs"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9",key:"yxxnd0"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9",key:"1p4y9e"}]]);var g=c(99891),h=c(67760),i=c(64398),j=c(71134);let k=[{icon:f,title:"Comprehensive Care",description:"Complete range of dental services"},{icon:g.A,title:"Advanced Technology",description:"State-of-the-art equipment"},{icon:h.A,title:"Gentle Approach",description:"Comfortable, pain-free treatments"},{icon:i.A,title:"Expert Team",description:"Experienced dental professionals"}];function l(){return(0,d.jsxs)(j.w,{background:"gray",className:"pt-32 pb-20",children:[(0,d.jsx)("div",{className:"text-center max-w-4xl mx-auto mb-16",children:(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,d.jsx)("p",{className:"text-primary font-semibold text-sm uppercase tracking-wide mb-4",children:"Our Services"}),(0,d.jsxs)("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6",children:["Complete"," ",(0,d.jsx)("span",{className:"text-primary",children:"Dental Care"})," ","Solutions"]}),(0,d.jsx)("p",{className:"text-xl text-gray-600 leading-relaxed",children:"From routine cleanings to advanced procedures, we offer comprehensive dental services to keep your smile healthy, beautiful, and confident."})]})}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:k.map((a,b)=>(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center hover:shadow-md transition-shadow duration-300",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(a.icon,{className:"h-8 w-8 text-primary"})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:a.title}),(0,d.jsx)("p",{className:"text-gray-600 text-sm",children:a.description})]},a.title))})]})}},79882:(a,b,c)=>{"use strict";c.d(b,{default:()=>o});var d=c(60687),e=c(51743),f=c(85814),g=c.n(f),h=c(48340),i=c(40228);let j=(0,c(62688).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);var k=c(71134),l=c(29523),m=c(44493),n=c(83446);function o(){return(0,d.jsx)(k.w,{background:"white",children:(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-r from-primary to-accent text-white rounded-2xl p-12 mb-12",children:[(0,d.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold mb-4",children:"Ready to Transform Your Smile?"}),(0,d.jsx)("p",{className:"text-xl opacity-90 mb-8 max-w-2xl mx-auto",children:"Don't wait to get the dental care you deserve. Book your appointment today and take the first step towards a healthier, more confident smile."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(l.$,{asChild:!0,size:"lg",variant:"secondary",className:"text-lg px-8 py-6",children:(0,d.jsx)(g(),{href:"/contact",children:"Book Appointment Now"})}),(0,d.jsx)(l.$,{asChild:!0,size:"lg",variant:"outline",className:"text-lg px-8 py-6 border-white text-white hover:bg-white hover:text-primary",children:(0,d.jsx)(g(),{href:"tel:{contactInfo.phone}",children:"Call Us Today"})})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1},viewport:{once:!0},children:(0,d.jsx)(m.Zp,{className:"hover:shadow-lg transition-shadow duration-300",children:(0,d.jsxs)(m.Wu,{className:"p-6 text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(h.A,{className:"h-8 w-8 text-primary"})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Call Us"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Speak directly with our friendly staff to schedule your appointment"}),(0,d.jsx)("p",{className:"text-primary font-semibold",children:n.r_.phone})]})})}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},children:(0,d.jsx)(m.Zp,{className:"hover:shadow-lg transition-shadow duration-300",children:(0,d.jsxs)(m.Wu,{className:"p-6 text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(i.A,{className:"h-8 w-8 text-primary"})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Online Booking"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Use our convenient online form to request an appointment"}),(0,d.jsx)(l.$,{asChild:!0,variant:"outline",size:"sm",children:(0,d.jsx)(g(),{href:"/contact",children:"Book Online"})})]})})}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},viewport:{once:!0},children:(0,d.jsx)(m.Zp,{className:"hover:shadow-lg transition-shadow duration-300",children:(0,d.jsxs)(m.Wu,{className:"p-6 text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(j,{className:"h-8 w-8 text-primary"})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Ask Questions"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Have questions about our services? We're here to help"}),(0,d.jsx)("p",{className:"text-primary font-semibold",children:n.r_.email})]})})})]})]})})}},82679:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]])},84677:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\dental\\\\dental-clinic\\\\src\\\\components\\\\sections\\\\ServicesCTA.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\components\\sections\\ServicesCTA.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},92360:(a,b,c)=>{"use strict";c.d(b,{default:()=>m});var d=c(60687),e=c(51743),f=c(40228),g=c(62688);let h=(0,g.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var i=c(82679);let j=(0,g.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var k=c(71134);let l=[{icon:f.A,title:"Book Appointment",description:"Schedule your visit through phone, email, or our online booking system. We offer flexible timing to suit your schedule.",step:"01"},{icon:h,title:"Comprehensive Examination",description:"Our dentist will conduct a thorough examination, including X-rays if needed, to assess your oral health.",step:"02"},{icon:i.A,title:"Treatment Planning",description:"We discuss your treatment options, explain procedures, and create a personalized treatment plan that fits your needs.",step:"03"},{icon:j,title:"Quality Treatment",description:"Receive expert dental care using advanced techniques and equipment, ensuring comfortable and effective treatment.",step:"04"}];function m(){return(0,d.jsxs)(k.w,{background:"gray",children:[(0,d.jsx)(k.X,{subtitle:"How It Works",title:"Our Treatment Process",description:"We follow a systematic approach to ensure you receive the best possible dental care from consultation to completion."}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:l.map((a,b)=>(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},viewport:{once:!0},className:"relative",children:[b<l.length-1&&(0,d.jsx)("div",{className:"hidden lg:block absolute top-16 left-full w-full h-0.5 bg-primary/20 z-0"}),(0,d.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 relative z-10 text-center hover:shadow-md transition-shadow duration-300",children:[(0,d.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,d.jsx)("div",{className:"w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold",children:a.step})}),(0,d.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4 mt-4",children:(0,d.jsx)(a.icon,{className:"h-8 w-8 text-primary"})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:a.title}),(0,d.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:a.description})]})]},a.title))}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mt-16",children:(0,d.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Why Choose Our Process?"}),(0,d.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Our systematic approach ensures that every patient receives personalized, high-quality care tailored to their specific needs."})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,d.jsx)(j,{className:"h-6 w-6 text-green-600"})}),(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Personalized Care"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Every treatment plan is customized to your unique needs"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,d.jsx)(j,{className:"h-6 w-6 text-blue-600"})}),(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Transparent Communication"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Clear explanation of procedures and costs upfront"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,d.jsx)(j,{className:"h-6 w-6 text-purple-600"})}),(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Quality Assurance"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Follow-up care to ensure optimal treatment outcomes"})]})]})]})})]})}},96834:(a,b,c)=>{"use strict";c.d(b,{E:()=>i});var d=c(60687);c(43210);var e=c(81391),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:a,variant:b,asChild:c=!1,...f}){let i=c?e.DX:"span";return(0,d.jsx)(i,{"data-slot":"badge",className:(0,g.cn)(h({variant:b}),a),...f})}},99891:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,472,753],()=>b(b.s=63836));module.exports=c})();