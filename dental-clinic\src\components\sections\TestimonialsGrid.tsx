'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Filter } from 'lucide-react';
import { Section, SectionHeader } from '@/components/ui/section';
import { Button } from '@/components/ui/button';
import TestimonialCard from '@/components/ui/testimonial-card';
import { testimonials } from '@/data/mockData';

export default function TestimonialsGrid() {
  const [selectedService, setSelectedService] = useState<string>('all');

  // Get unique services from testimonials
  const services = ['all', ...Array.from(new Set(testimonials.map(t => t.service)))];
  
  const filteredTestimonials = selectedService === 'all' 
    ? testimonials 
    : testimonials.filter(t => t.service === selectedService);

  return (
    <Section background="white">
      <SectionHeader
        subtitle="Patient Reviews"
        title="Real Stories from Real Patients"
        description="Read authentic testimonials from our patients who have experienced our dental care services. Their stories speak to our commitment to excellence."
      />

      {/* Service Filter */}
      <div className="flex flex-wrap justify-center gap-3 mb-12">
        <div className="flex items-center space-x-2 text-gray-600 mr-4">
          <Filter className="h-4 w-4" />
          <span className="text-sm font-medium">Filter by service:</span>
        </div>
        {services.map((service) => (
          <Button
            key={service}
            variant={selectedService === service ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedService(service)}
          >
            {service === 'all' ? 'All Services' : service}
          </Button>
        ))}
      </div>

      {/* Testimonials Grid */}
      <motion.div 
        layout
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
      >
        {filteredTestimonials.map((testimonial, index) => (
          <TestimonialCard
            key={testimonial.id}
            testimonial={testimonial}
            index={index}
            showService={selectedService === 'all'}
          />
        ))}
      </motion.div>

      {/* No Results Message */}
      {filteredTestimonials.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <p className="text-gray-600 text-lg">
            No testimonials found for the selected service.
          </p>
          <Button
            variant="outline"
            onClick={() => setSelectedService('all')}
            className="mt-4"
          >
            Show All Testimonials
          </Button>
        </motion.div>
      )}

      {/* Call to Action */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="mt-16 text-center"
      >
        <div className="bg-gradient-to-r from-primary/5 to-accent/5 rounded-2xl p-8 border border-primary/10">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Share Your Experience
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Have you been treated at our clinic? We'd love to hear about your experience. 
            Your feedback helps us improve and helps other patients make informed decisions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg">
              Leave a Review
            </Button>
            <Button variant="outline" size="lg">
              Contact Us
            </Button>
          </div>
        </div>
      </motion.div>
    </Section>
  );
}
