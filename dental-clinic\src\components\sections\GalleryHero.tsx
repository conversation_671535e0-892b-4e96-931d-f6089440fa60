'use client';

import { motion } from 'framer-motion';
import { Camera, Building, Stethoscope, Users } from 'lucide-react';
import { Section } from '@/components/ui/section';

const galleryStats = [
  {
    icon: Building,
    title: 'Modern Facilities',
    count: '6+',
    description: 'Treatment rooms'
  },
  {
    icon: Stethoscope,
    title: 'Advanced Equipment',
    count: '15+',
    description: 'Medical devices'
  },
  {
    icon: Users,
    title: 'Professional Team',
    count: '8+',
    description: 'Staff members'
  },
  {
    icon: Camera,
    title: 'Photo Gallery',
    count: '50+',
    description: 'Images to explore'
  }
];

export default function GalleryHero() {
  return (
    <Section background="gray" className="pt-32 pb-20">
      <div className="text-center max-w-4xl mx-auto mb-16">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <p className="text-primary font-semibold text-sm uppercase tracking-wide mb-4">
            Our Gallery
          </p>
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Take a{' '}
            <span className="text-primary">Virtual Tour</span>{' '}
            of Our Clinic
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed">
            Explore our modern facilities, state-of-the-art equipment, and welcoming environment. 
            See why patients choose us for their dental care needs.
          </p>
        </motion.div>
      </div>

      {/* Gallery Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {galleryStats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center hover:shadow-md transition-shadow duration-300"
          >
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <stat.icon className="h-8 w-8 text-primary" />
            </div>
            <div className="text-3xl font-bold text-primary mb-2">
              {stat.count}
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">
              {stat.title}
            </h3>
            <p className="text-gray-600 text-sm">
              {stat.description}
            </p>
          </motion.div>
        ))}
      </div>
    </Section>
  );
}
