'use client';

import { motion } from 'framer-motion';
import { <PERSON>, Quote, Heart, Users } from 'lucide-react';
import { Section } from '@/components/ui/section';

const ratingStats = [
  {
    icon: Star,
    title: 'Average Rating',
    value: '4.9/5',
    description: 'Based on 500+ reviews'
  },
  {
    icon: Users,
    title: 'Happy Patients',
    value: '5000+',
    description: 'Served since 2015'
  },
  {
    icon: Heart,
    title: 'Satisfaction Rate',
    value: '98%',
    description: 'Would recommend us'
  },
  {
    icon: Quote,
    title: 'Testimonials',
    value: '200+',
    description: 'Written reviews'
  }
];

export default function TestimonialsHero() {
  return (
    <Section background="gray" className="pt-32 pb-20">
      <div className="text-center max-w-4xl mx-auto mb-16">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <p className="text-primary font-semibold text-sm uppercase tracking-wide mb-4">
            Patient Testimonials
          </p>
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            What Our{' '}
            <span className="text-primary">Patients Say</span>
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed mb-8">
            Don&apos;t just take our word for it. Read genuine reviews from our satisfied patients
            who have experienced our exceptional dental care firsthand.
          </p>

          {/* Overall Rating Display */}
          <div className="inline-flex items-center space-x-2 bg-white px-8 py-4 rounded-full shadow-sm border border-gray-100">
            <div className="flex space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star key={star} className="h-6 w-6 text-yellow-400 fill-current" />
              ))}
            </div>
            <span className="text-2xl font-bold text-gray-900">4.9</span>
            <span className="text-gray-600">out of 5 stars</span>
          </div>
        </motion.div>
      </div>

      {/* Rating Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {ratingStats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center hover:shadow-md transition-shadow duration-300"
          >
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <stat.icon className="h-8 w-8 text-primary" />
            </div>
            <div className="text-3xl font-bold text-primary mb-2">
              {stat.value}
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">
              {stat.title}
            </h3>
            <p className="text-gray-600 text-sm">
              {stat.description}
            </p>
          </motion.div>
        ))}
      </div>
    </Section>
  );
}
