{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/dental/dental-clinic/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/dental/dental-clinic/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/dental/dental-clinic/src/data/mockData.ts"], "sourcesContent": ["import { Service, TeamMember, Testimonial, GalleryImage, ContactInfo, ClinicInfo } from '@/types';\n\nexport const clinicInfo: ClinicInfo = {\n  name: \"Dental Care Clinic Biratnagar\",\n  tagline: \"Your Smile, Our Priority\",\n  mission: \"To provide exceptional dental care with compassion, using the latest technology and techniques to ensure every patient receives the highest quality treatment in a comfortable, welcoming environment.\",\n  story: \"Established in 2015, Dental Care Clinic Biratnagar has been serving the community with dedication and excellence. Our clinic was founded with the vision of making quality dental care accessible to everyone in Biratnagar and surrounding areas.\",\n  established: \"2015\",\n  location: \"Biratnagar, Nepal\"\n};\n\nexport const services: Service[] = [\n  {\n    id: \"1\",\n    name: \"Dental Cleaning & Checkups\",\n    description: \"Comprehensive oral examination and professional teeth cleaning to maintain optimal oral health.\",\n    benefits: [\n      \"Prevents cavities and gum disease\",\n      \"Early detection of dental problems\",\n      \"Fresh breath and clean feeling\",\n      \"Maintains overall oral health\"\n    ],\n    procedure: \"Our dental hygienists perform thorough cleaning using ultrasonic scalers and hand instruments, followed by polishing and fluoride treatment.\",\n    image: \"/images/services/cleaning.jpg\",\n    price: \"NPR 1,500 - 2,500\"\n  },\n  {\n    id: \"2\",\n    name: \"Teeth Whitening\",\n    description: \"Professional teeth whitening treatments to brighten your smile and boost your confidence.\",\n    benefits: [\n      \"Removes stains and discoloration\",\n      \"Safe and effective results\",\n      \"Boosts self-confidence\",\n      \"Long-lasting whitening effect\"\n    ],\n    procedure: \"We use professional-grade whitening gel applied with custom trays or in-office laser whitening for immediate results.\",\n    image: \"/images/services/whitening.jpg\",\n    price: \"NPR 8,000 - 15,000\"\n  },\n  {\n    id: \"3\",\n    name: \"Orthodontics\",\n    description: \"Comprehensive orthodontic treatment including braces and aligners to straighten teeth and correct bite issues.\",\n    benefits: [\n      \"Straighter, more attractive smile\",\n      \"Improved bite function\",\n      \"Better oral hygiene\",\n      \"Enhanced self-esteem\"\n    ],\n    procedure: \"Treatment begins with comprehensive examination, followed by custom treatment plan using traditional braces or clear aligners.\",\n    image: \"/images/services/orthodontics.jpg\",\n    price: \"NPR 50,000 - 150,000\"\n  },\n  {\n    id: \"4\",\n    name: \"Dental Implants\",\n    description: \"Permanent tooth replacement solution using titanium implants for missing teeth.\",\n    benefits: [\n      \"Permanent tooth replacement\",\n      \"Natural look and feel\",\n      \"Preserves jawbone structure\",\n      \"No impact on adjacent teeth\"\n    ],\n    procedure: \"Surgical placement of titanium implant into jawbone, followed by healing period and crown placement.\",\n    image: \"/images/services/implants.jpg\",\n    price: \"NPR 80,000 - 120,000\"\n  },\n  {\n    id: \"5\",\n    name: \"Root Canal Treatment\",\n    description: \"Advanced endodontic treatment to save infected or severely damaged teeth.\",\n    benefits: [\n      \"Saves natural tooth\",\n      \"Eliminates pain and infection\",\n      \"Prevents further complications\",\n      \"Cost-effective tooth preservation\"\n    ],\n    procedure: \"Removal of infected pulp, cleaning and disinfection of root canals, followed by filling and crown placement.\",\n    image: \"/images/services/root-canal.jpg\",\n    price: \"NPR 8,000 - 15,000\"\n  },\n  {\n    id: \"6\",\n    name: \"Cosmetic Dentistry\",\n    description: \"Aesthetic dental treatments including veneers, bonding, and smile makeovers.\",\n    benefits: [\n      \"Enhanced smile appearance\",\n      \"Improved facial aesthetics\",\n      \"Boosted confidence\",\n      \"Customized treatment plans\"\n    ],\n    procedure: \"Comprehensive smile analysis followed by customized treatment using veneers, bonding, or other cosmetic procedures.\",\n    image: \"/images/services/cosmetic.jpg\",\n    price: \"NPR 15,000 - 80,000\"\n  }\n];\n\nexport const testimonials: Testimonial[] = [\n  {\n    id: \"1\",\n    name: \"Sita Rai\",\n    city: \"Biratnagar\",\n    rating: 5,\n    review: \"Excellent service! Dr. Rajesh performed my dental implant surgery with great care. The staff is very professional and the clinic is very clean. Highly recommended!\",\n    service: \"Dental Implants\",\n    date: \"2024-01-15\",\n    image: \"/images/testimonials/sita.jpg\"\n  },\n  {\n    id: \"2\",\n    name: \"Ramesh Gurung\",\n    city: \"Dharan\",\n    rating: 5,\n    review: \"I had my teeth whitening done here and the results are amazing! Dr. Priya explained everything clearly and the treatment was painless. Very satisfied with the service.\",\n    service: \"Teeth Whitening\",\n    date: \"2024-02-20\",\n    image: \"/images/testimonials/ramesh.jpg\"\n  },\n  {\n    id: \"3\",\n    name: \"Maya Shrestha\",\n    city: \"Biratnagar\",\n    rating: 5,\n    review: \"The orthodontic treatment for my daughter was excellent. Dr. Priya was very patient and gentle with her. The braces were fitted perfectly and the results are wonderful.\",\n    service: \"Orthodontics\",\n    date: \"2024-03-10\",\n    image: \"/images/testimonials/maya.jpg\"\n  },\n  {\n    id: \"4\",\n    name: \"Bikash Limbu\",\n    city: \"Itahari\",\n    rating: 4,\n    review: \"Great experience with root canal treatment. Dr. Amit made sure I was comfortable throughout the procedure. The clinic has modern equipment and very hygienic environment.\",\n    service: \"Root Canal Treatment\",\n    date: \"2024-03-25\",\n    image: \"/images/testimonials/bikash.jpg\"\n  },\n  {\n    id: \"5\",\n    name: \"Sunita Karki\",\n    city: \"Biratnagar\",\n    rating: 5,\n    review: \"Regular dental checkups here have kept my teeth healthy. The staff is friendly and the doctors are very knowledgeable. Best dental clinic in Biratnagar!\",\n    service: \"Dental Cleaning\",\n    date: \"2024-04-05\",\n    image: \"/images/testimonials/sunita.jpg\"\n  }\n];\n\nexport const galleryImages: GalleryImage[] = [\n  {\n    id: \"1\",\n    src: \"/images/gallery/reception.jpg\",\n    alt: \"Modern reception area\",\n    category: \"facility\",\n    title: \"Reception Area\",\n    description: \"Comfortable and welcoming reception area\"\n  },\n  {\n    id: \"2\",\n    src: \"/images/gallery/treatment-room.jpg\",\n    alt: \"Treatment room with modern equipment\",\n    category: \"facility\",\n    title: \"Treatment Room\",\n    description: \"State-of-the-art treatment rooms\"\n  },\n  {\n    id: \"3\",\n    src: \"/images/gallery/dental-chair.jpg\",\n    alt: \"Modern dental chair\",\n    category: \"equipment\",\n    title: \"Dental Chair\",\n    description: \"Comfortable modern dental chairs\"\n  },\n  {\n    id: \"4\",\n    src: \"/images/gallery/xray-machine.jpg\",\n    alt: \"Digital X-ray machine\",\n    category: \"equipment\",\n    title: \"Digital X-Ray\",\n    description: \"Advanced digital X-ray technology\"\n  },\n  {\n    id: \"5\",\n    src: \"/images/gallery/sterilization.jpg\",\n    alt: \"Sterilization equipment\",\n    category: \"equipment\",\n    title: \"Sterilization Unit\",\n    description: \"Advanced sterilization equipment\"\n  },\n  {\n    id: \"6\",\n    src: \"/images/gallery/team-photo.jpg\",\n    alt: \"Dental team photo\",\n    category: \"team\",\n    title: \"Our Team\",\n    description: \"Professional dental care team\"\n  }\n];\n\nexport const contactInfo: ContactInfo = {\n  address: \"Main Road, Biratnagar-10, Morang, Nepal\",\n  phone: \"+977-21-525678\",\n  email: \"<EMAIL>\",\n  emergencyPhone: \"+977-9841234567\",\n  businessHours: {\n    \"Sunday\": \"10:00 AM - 6:00 PM\",\n    \"Monday\": \"10:00 AM - 6:00 PM\",\n    \"Tuesday\": \"10:00 AM - 6:00 PM\",\n    \"Wednesday\": \"10:00 AM - 6:00 PM\",\n    \"Thursday\": \"10:00 AM - 6:00 PM\",\n    \"Friday\": \"10:00 AM - 6:00 PM\",\n    \"Saturday\": \"Closed\"\n  },\n  socialMedia: {\n    facebook: \"https://facebook.com/dentalcarebiratnagar\",\n    instagram: \"https://instagram.com/dentalcarebiratnagar\",\n    twitter: \"https://twitter.com/dentalcarebiratnagar\"\n  },\n  mapEmbedUrl: \"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3571.5234567890123!2d87.2734567890123!3d26.4567890123456!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjbCsDI3JzI0LjQiTiA4N8KwMTYnMjQuNCJF!5e0!3m2!1sen!2snp!4v1234567890123!5m2!1sen!2snp\"\n};\n\nexport const teamMembers: TeamMember[] = [\n  {\n    id: \"1\",\n    name: \"Dr. Rajesh Sharma\",\n    role: \"Chief Dentist & Founder\",\n    qualification: \"BDS, MDS (Oral & Maxillofacial Surgery)\",\n    experience: \"12+ years\",\n    bio: \"Dr. Rajesh Sharma is a highly experienced oral and maxillofacial surgeon with over 12 years of practice. He founded Dental Care Clinic Biratnagar with the vision of providing world-class dental care to the community.\",\n    image: \"/images/team/dr-rajesh.jpg\",\n    specialties: [\"Oral Surgery\", \"Dental Implants\", \"Complex Extractions\", \"Facial Trauma\"]\n  },\n  {\n    id: \"2\",\n    name: \"Dr. Priya Adhikari\",\n    role: \"Orthodontist\",\n    qualification: \"BDS, MDS (Orthodontics)\",\n    experience: \"8+ years\",\n    bio: \"Dr. Priya Adhikari specializes in orthodontics and has helped hundreds of patients achieve beautiful, straight smiles. She is known for her gentle approach and attention to detail.\",\n    image: \"/images/team/dr-priya.jpg\",\n    specialties: [\"Braces\", \"Clear Aligners\", \"Pediatric Orthodontics\", \"Bite Correction\"]\n  },\n  {\n    id: \"3\",\n    name: \"Dr. Amit Thapa\",\n    role: \"Endodontist\",\n    qualification: \"BDS, MDS (Endodontics)\",\n    experience: \"6+ years\",\n    bio: \"Dr. Amit Thapa is our endodontic specialist, focusing on root canal treatments and saving natural teeth. His expertise in pain management ensures comfortable treatment experiences.\",\n    image: \"/images/team/dr-amit.jpg\",\n    specialties: [\"Root Canal Treatment\", \"Endodontic Surgery\", \"Pain Management\", \"Dental Trauma\"]\n  }\n];\n"], "names": [], "mappings": ";;;;;;;;AAEO,MAAM,aAAyB;IACpC,MAAM;IACN,SAAS;IACT,SAAS;IACT,OAAO;IACP,aAAa;IACb,UAAU;AACZ;AAEO,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,WAAW;QACX,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,WAAW;QACX,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,WAAW;QACX,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,WAAW;QACX,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,WAAW;QACX,OAAO;QACP,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,WAAW;QACX,OAAO;QACP,OAAO;IACT;CACD;AAEM,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,MAAM;QACN,OAAO;IACT;CACD;AAEM,MAAM,gBAAgC;IAC3C;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,UAAU;QACV,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,UAAU;QACV,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,UAAU;QACV,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,UAAU;QACV,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,UAAU;QACV,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,UAAU;QACV,OAAO;QACP,aAAa;IACf;CACD;AAEM,MAAM,cAA2B;IACtC,SAAS;IACT,OAAO;IACP,OAAO;IACP,gBAAgB;IAChB,eAAe;QACb,UAAU;QACV,UAAU;QACV,WAAW;QACX,aAAa;QACb,YAAY;QACZ,UAAU;QACV,YAAY;IACd;IACA,aAAa;QACX,UAAU;QACV,WAAW;QACX,SAAS;IACX;IACA,aAAa;AACf;AAEO,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,eAAe;QACf,YAAY;QACZ,KAAK;QACL,OAAO;QACP,aAAa;YAAC;YAAgB;YAAmB;YAAuB;SAAgB;IAC1F;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,eAAe;QACf,YAAY;QACZ,KAAK;QACL,OAAO;QACP,aAAa;YAAC;YAAU;YAAkB;YAA0B;SAAkB;IACxF;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,eAAe;QACf,YAAY;QACZ,KAAK;QACL,OAAO;QACP,aAAa;YAAC;YAAwB;YAAsB;YAAmB;SAAgB;IACjG;CACD", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/dental/dental-clinic/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport { Menu, X, Phone, MapPin } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { clinicInfo, contactInfo } from '@/data/mockData';\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'About', href: '/about' },\n  { name: 'Services', href: '/services' },\n  { name: 'Gallery', href: '/gallery' },\n  { name: 'Testimonials', href: '/testimonials' },\n  { name: 'Contact', href: '/contact' },\n];\n\nexport default function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n\n  return (\n    <header className=\"bg-white shadow-sm sticky top-0 z-50\">\n      {/* Top bar */}\n      <div className=\"bg-primary text-primary-foreground py-2\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center text-sm\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-1\">\n                <Phone className=\"h-4 w-4\" />\n                <span>{contactInfo.phone}</span>\n              </div>\n              <div className=\"hidden sm:flex items-center space-x-1\">\n                <MapPin className=\"h-4 w-4\" />\n                <span>{contactInfo.address}</span>\n              </div>\n            </div>\n            <div className=\"hidden md:block\">\n              <span>Emergency: {contactInfo.emergencyPhone}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main header */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"w-10 h-10 bg-primary rounded-full flex items-center justify-center\">\n                <span className=\"text-primary-foreground font-bold text-lg\">DC</span>\n              </div>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900\">{clinicInfo.name}</h1>\n                <p className=\"text-sm text-gray-600\">{clinicInfo.tagline}</p>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-700 hover:text-primary transition-colors duration-200 font-medium\"\n              >\n                {item.name}\n              </Link>\n            ))}\n          </nav>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:block\">\n            <Button asChild>\n              <Link href=\"/contact\">Book Appointment</Link>\n            </Button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n            >\n              {mobileMenuOpen ? (\n                <X className=\"h-6 w-6\" />\n              ) : (\n                <Menu className=\"h-6 w-6\" />\n              )}\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {mobileMenuOpen && (\n        <motion.div\n          initial={{ opacity: 0, height: 0 }}\n          animate={{ opacity: 1, height: 'auto' }}\n          exit={{ opacity: 0, height: 0 }}\n          className=\"md:hidden bg-white border-t\"\n        >\n          <div className=\"px-4 py-2 space-y-1\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"block px-3 py-2 text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md transition-colors duration-200\"\n                onClick={() => setMobileMenuOpen(false)}\n              >\n                {item.name}\n              </Link>\n            ))}\n            <div className=\"pt-2\">\n              <Button asChild className=\"w-full\">\n                <Link href=\"/contact\" onClick={() => setMobileMenuOpen(false)}>\n                  Book Appointment\n                </Link>\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n      )}\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAgB,MAAM;IAAgB;IAC9C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEc,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAM,uHAAA,CAAA,cAAW,CAAC,KAAK;;;;;;;;;;;;kDAE1B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAM,uHAAA,CAAA,cAAW,CAAC,OAAO;;;;;;;;;;;;;;;;;;0CAG9B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;;wCAAK;wCAAY,uHAAA,CAAA,cAAW,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOpD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;kDAE9D,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmC,uHAAA,CAAA,aAAU,CAAC,IAAI;;;;;;0DAChE,8OAAC;gDAAE,WAAU;0DAAyB,uHAAA,CAAA,aAAU,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;sCAM9D,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,IAAI;mCAJL,KAAK,IAAI;;;;;;;;;;sCAUpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAW;;;;;;;;;;;;;;;;sCAK1B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,kBAAkB,CAAC;0CAEjC,+BACC,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAEb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQzB,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;gBACjC,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAO;gBACtC,MAAM;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;gBAC9B,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,kBAAkB;0CAEhC,KAAK,IAAI;+BALL,KAAK,IAAI;;;;;sCAQlB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO;gCAAC,WAAU;0CACxB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,SAAS,IAAM,kBAAkB;8CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU/E", "debugId": null}}]}