'use client';

import { motion } from 'framer-motion';
import { Calendar, MapPin, Users, Award } from 'lucide-react';
import { Section } from '@/components/ui/section';
import { clinicInfo } from '@/data/mockData';

const milestones = [
  {
    year: '2015',
    title: 'Clinic Founded',
    description: 'Dr. <PERSON><PERSON> established Dental Care Clinic Biratnagar with a vision to provide world-class dental care to the local community.',
    icon: Calendar
  },
  {
    year: '2017',
    title: 'Team Expansion',
    description: 'Added specialized dentists including orthodontist Dr. <PERSON><PERSON> and endodontist Dr. <PERSON><PERSON> to our growing team.',
    icon: Users
  },
  {
    year: '2019',
    title: 'Modern Equipment',
    description: 'Invested in state-of-the-art dental equipment including digital X-ray machines and advanced sterilization systems.',
    icon: Award
  },
  {
    year: '2024',
    title: 'Community Leader',
    description: 'Recognized as the leading dental clinic in Biratnagar, having served over 5,000 patients with exceptional care.',
    icon: MapPin
  }
];

export default function ClinicStory() {
  return (
    <Section background="white">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
        {/* Story Content */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="space-y-8"
        >
          <div>
            <p className="text-primary font-semibold text-sm uppercase tracking-wide mb-4">
              Our Story
            </p>
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              A Journey of{' '}
              <span className="text-primary">Excellence</span>
            </h2>
            <div className="space-y-4 text-gray-600 leading-relaxed">
              <p>
                {clinicInfo.story}
              </p>
              <p>
                What started as a small practice has grown into Biratnagar&apos;s most trusted dental clinic.
                Our commitment to excellence, combined with the latest dental technology and a compassionate approach,
                has made us the preferred choice for families throughout the region.
              </p>
              <p>
                We believe that everyone deserves access to quality dental care, which is why we&apos;ve made it our mission
                to provide comprehensive services at affordable prices, without compromising on quality or comfort.
              </p>
            </div>
          </div>

          {/* Values */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div className="bg-primary/5 p-6 rounded-xl">
              <h3 className="font-semibold text-gray-900 mb-2">Our Mission</h3>
              <p className="text-sm text-gray-600">
                To provide exceptional dental care with compassion and integrity.
              </p>
            </div>
            <div className="bg-accent/5 p-6 rounded-xl">
              <h3 className="font-semibold text-gray-900 mb-2">Our Vision</h3>
              <p className="text-sm text-gray-600">
                To be the leading dental clinic in Nepal, setting standards for quality care.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Timeline */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="space-y-8"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-2">Our Journey</h3>
            <p className="text-gray-600">Key milestones in our clinic&apos;s history</p>
          </div>

          <div className="space-y-6">
            {milestones.map((milestone, index) => (
              <motion.div
                key={milestone.year}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                    <milestone.icon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-lg font-bold text-primary">{milestone.year}</span>
                    <h4 className="text-lg font-semibold text-gray-900">{milestone.title}</h4>
                  </div>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {milestone.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </Section>
  );
}
