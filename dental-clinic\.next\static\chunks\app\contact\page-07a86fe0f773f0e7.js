(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{285:(e,a,t)=>{"use strict";t.d(a,{$:()=>o});var i=t(5155);t(2115);var s=t(4624),r=t(2085),n=t(9434);let l=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:a,variant:t,size:r,asChild:o=!1,...c}=e,d=o?s.DX:"button";return(0,i.jsx)(d,{"data-slot":"button",className:(0,n.cn)(l({variant:t,size:r,className:a})),...c})}},1789:(e,a,t)=>{"use strict";t.d(a,{default:()=>p});var i=t(5155),s=t(2605);let r=(0,t(9946).A)("navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]]);var n=t(4516),l=t(9420),o=t(4186),c=t(4090),d=t(285),m=t(6695),h=t(8720);let x=["City Mall - 2 minutes walk","Main Chowk - 1 minute walk","Biratnagar Hospital - 5 minutes drive","Bus Station - 10 minutes walk"];function p(){return(0,i.jsxs)(c.w,{background:"white",id:"location",children:[(0,i.jsx)(c.X,{subtitle:"Our Location",title:"Find Us in Biratnagar",description:"We're conveniently located in the heart of Biratnagar, easily accessible by public transport with parking available for patients."}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,i.jsx)(s.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"lg:col-span-2",children:(0,i.jsxs)(m.Zp,{className:"overflow-hidden",children:[(0,i.jsx)("div",{className:"h-96 bg-gray-200 relative",children:(0,i.jsx)("iframe",{src:h.r_.mapEmbedUrl,width:"100%",height:"100%",style:{border:0},allowFullScreen:!0,loading:"lazy",referrerPolicy:"no-referrer-when-downgrade",title:"Dental Care Clinic Biratnagar Location",className:"absolute inset-0"})}),(0,i.jsx)(m.Wu,{className:"p-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:"Dental Care Clinic Biratnagar"}),(0,i.jsx)("p",{className:"text-gray-600 text-sm",children:h.r_.address})]}),(0,i.jsx)(d.$,{asChild:!0,variant:"outline",size:"sm",children:(0,i.jsxs)("a",{href:"https://www.google.com/maps/search/?api=1&query=".concat(encodeURIComponent(h.r_.address)),target:"_blank",rel:"noopener noreferrer",children:[(0,i.jsx)(r,{className:"h-4 w-4 mr-2"}),"Get Directions"]})})]})})]})}),(0,i.jsxs)(s.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-6",children:[(0,i.jsx)(m.Zp,{children:(0,i.jsxs)(m.Wu,{className:"p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Contact"}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)(n.A,{className:"h-5 w-5 text-primary flex-shrink-0"}),(0,i.jsx)("p",{className:"text-gray-600 text-sm",children:h.r_.address})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)(l.A,{className:"h-5 w-5 text-primary flex-shrink-0"}),(0,i.jsx)("a",{href:"tel:".concat(h.r_.phone),className:"text-primary font-semibold text-sm hover:underline",children:h.r_.phone})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)(o.A,{className:"h-5 w-5 text-primary flex-shrink-0"}),(0,i.jsxs)("div",{className:"text-gray-600 text-sm",children:[(0,i.jsx)("p",{children:"Mon-Fri: 10:00 AM - 6:00 PM"}),(0,i.jsx)("p",{children:"Sun: 10:00 AM - 6:00 PM"})]})]})]})]})}),(0,i.jsx)(m.Zp,{children:(0,i.jsxs)(m.Wu,{className:"p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Nearby Landmarks"}),(0,i.jsx)("div",{className:"space-y-2",children:x.map((e,a)=>(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full flex-shrink-0"}),(0,i.jsx)("p",{className:"text-gray-600 text-sm",children:e})]},a))})]})}),(0,i.jsx)(m.Zp,{children:(0,i.jsxs)(m.Wu,{className:"p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Parking & Access"}),(0,i.jsxs)("div",{className:"space-y-3 text-sm text-gray-600",children:[(0,i.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"}),(0,i.jsx)("p",{children:"Free parking available for patients"})]}),(0,i.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"}),(0,i.jsx)("p",{children:"Wheelchair accessible entrance"})]}),(0,i.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"}),(0,i.jsx)("p",{children:"Ground floor location"})]}),(0,i.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"}),(0,i.jsx)("p",{children:"Public transport accessible"})]})]})]})})]})]}),(0,i.jsx)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mt-16 text-center",children:(0,i.jsxs)("div",{className:"bg-gradient-to-r from-primary to-accent text-white rounded-2xl p-8",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold mb-4",children:"Ready to Visit Us?"}),(0,i.jsx)("p",{className:"text-xl opacity-90 mb-6",children:"Schedule your appointment today and experience exceptional dental care in the heart of Biratnagar."}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,i.jsx)(d.$,{asChild:!0,size:"lg",variant:"secondary",className:"text-lg px-8 py-6",children:(0,i.jsxs)("a",{href:"tel:".concat(h.r_.phone),children:["Call Now: ",h.r_.phone]})}),(0,i.jsx)(d.$,{asChild:!0,size:"lg",variant:"outline",className:"text-lg px-8 py-6 border-white text-white hover:bg-white hover:text-primary",children:(0,i.jsx)("a",{href:"#contact-form",children:"Book Online"})})]})]})})]})}},2072:(e,a,t)=>{"use strict";t.d(a,{A:()=>g});var i=t(5155),s=t(2115),r=t(2605),n=t(285),l=t(9434);function o(e){let{className:a,type:t,...s}=e;return(0,i.jsx)("input",{type:t,"data-slot":"input",className:(0,l.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...s})}function c(e){let{className:a,...t}=e;return(0,i.jsx)("textarea",{"data-slot":"textarea",className:(0,l.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...t})}t(7650);var d=t(4624),m=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,a)=>{let t=(0,d.TL)(`Primitive.${a}`),r=s.forwardRef((e,s)=>{let{asChild:r,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(r?t:a,{...n,ref:s})});return r.displayName=`Primitive.${a}`,{...e,[a]:r}},{}),h=s.forwardRef((e,a)=>(0,i.jsx)(m.label,{...e,ref:a,onMouseDown:a=>{var t;a.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));function x(e){let{className:a,...t}=e;return(0,i.jsx)(h,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...t})}h.displayName="Label";var p=t(6695);function g(e){let{title:a="Send us a Message",showCard:t=!0}=e,[l,d]=(0,s.useState)({name:"",email:"",phone:"",subject:"",message:""}),[m,h]=(0,s.useState)(!1),[g,u]=(0,s.useState)(!1),y=e=>{d(a=>({...a,[e.target.name]:e.target.value}))},f=async e=>{e.preventDefault(),h(!0),await new Promise(e=>setTimeout(e,1e3)),h(!1),u(!0),setTimeout(()=>{u(!1),d({name:"",email:"",phone:"",subject:"",message:""})},3e3)},b=()=>(0,i.jsxs)("div",{className:"space-y-6",children:[a&&(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:a}),(0,i.jsx)("p",{className:"text-gray-600",children:"We'd love to hear from you. Send us a message and we'll respond as soon as possible."})]}),g?(0,i.jsxs)(r.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"text-center py-8",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,i.jsx)("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,i.jsx)("h4",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Message Sent!"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Thank you for contacting us. We'll get back to you soon."})]}):(0,i.jsxs)("form",{onSubmit:f,className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(x,{htmlFor:"name",children:"Full Name *"}),(0,i.jsx)(o,{id:"name",name:"name",type:"text",required:!0,value:l.name,onChange:y,placeholder:"Your full name"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(x,{htmlFor:"email",children:"Email Address *"}),(0,i.jsx)(o,{id:"email",name:"email",type:"email",required:!0,value:l.email,onChange:y,placeholder:"<EMAIL>"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(x,{htmlFor:"phone",children:"Phone Number"}),(0,i.jsx)(o,{id:"phone",name:"phone",type:"tel",value:l.phone,onChange:y,placeholder:"+977-XXX-XXXXXX"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(x,{htmlFor:"subject",children:"Subject *"}),(0,i.jsx)(o,{id:"subject",name:"subject",type:"text",required:!0,value:l.subject,onChange:y,placeholder:"What is this regarding?"})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(x,{htmlFor:"message",children:"Message *"}),(0,i.jsx)(c,{id:"message",name:"message",required:!0,rows:5,value:l.message,onChange:y,placeholder:"Tell us how we can help you..."})]}),(0,i.jsx)(n.$,{type:"submit",className:"w-full",disabled:m,children:m?"Sending...":"Send Message"})]})]});return t?(0,i.jsx)(p.Zp,{children:(0,i.jsx)(p.Wu,{className:"p-6",children:(0,i.jsx)(b,{})})}):(0,i.jsx)(b,{})}},2648:(e,a,t)=>{"use strict";t.d(a,{default:()=>h});var i=t(5155),s=t(2605);let r=(0,t(9946).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);var n=t(9420),l=t(8883),o=t(9074),c=t(4090),d=t(2072);let m=[{icon:r,title:"Online Form",description:"Fill out our contact form and we'll get back to you within 24 hours",highlight:"Most Popular"},{icon:n.A,title:"Phone Call",description:"Speak directly with our staff to schedule your appointment",highlight:"Immediate Response"},{icon:l.A,title:"Email",description:"Send us detailed questions about our services or your dental needs",highlight:"Detailed Inquiries"},{icon:o.A,title:"Walk-in",description:"Visit our clinic during business hours for immediate assistance",highlight:"Emergency Cases"}];function h(){return(0,i.jsxs)(c.w,{background:"gray",children:[(0,i.jsx)(c.X,{subtitle:"Get In Touch",title:"Send Us a Message",description:"Choose the best way to contact us. We're here to answer your questions and help you schedule your dental care."}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,i.jsxs)(s.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-8",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Ways to Contact Us"}),(0,i.jsx)("p",{className:"text-gray-600 mb-8",children:"We offer multiple convenient ways to get in touch with our dental team. Choose the method that works best for you."})]}),(0,i.jsx)("div",{className:"space-y-6",children:m.map((e,a)=>(0,i.jsx)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*a},viewport:{once:!0},className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300",children:(0,i.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,i.jsx)(e.icon,{className:"h-6 w-6 text-primary"})}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,i.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:e.title}),(0,i.jsx)("span",{className:"text-xs bg-primary/10 text-primary px-2 py-1 rounded-full",children:e.highlight})]}),(0,i.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:e.description})]})]})},e.title))}),(0,i.jsxs)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"bg-gradient-to-r from-primary/5 to-accent/5 rounded-xl p-6 border border-primary/10",children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-900 mb-3",children:"Response Times"}),(0,i.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Online Form:"}),(0,i.jsx)("span",{className:"font-semibold text-gray-900",children:"Within 24 hours"})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Phone Calls:"}),(0,i.jsx)("span",{className:"font-semibold text-gray-900",children:"Immediate"})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Email:"}),(0,i.jsx)("span",{className:"font-semibold text-gray-900",children:"Within 24 hours"})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Emergency:"}),(0,i.jsx)("span",{className:"font-semibold text-red-600",children:"24/7 Available"})]})]})]})]}),(0,i.jsx)(s.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:(0,i.jsx)(d.A,{title:"Send us a Message",showCard:!0})})]})]})}},3322:(e,a,t)=>{"use strict";t.d(a,{default:()=>v});var i=t(5155),s=t(2605),r=t(4516),n=t(9420),l=t(8883),o=t(4186),c=t(9946);let d=(0,c.A)("car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]]),m=(0,c.A)("bus",[["path",{d:"M8 6v6",key:"18i7km"}],["path",{d:"M15 6v6",key:"1sg6z9"}],["path",{d:"M2 12h19.6",key:"de5uta"}],["path",{d:"M18 18h3s.5-1.7.8-2.8c.1-.4.2-.8.2-1.2 0-.4-.1-.8-.2-1.2l-1.4-5C20.1 6.8 19.1 6 18 6H4a2 2 0 0 0-2 2v10h3",key:"1wwztk"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}],["path",{d:"M9 18h5",key:"lrx6i"}],["circle",{cx:"16",cy:"18",r:"2",key:"1v4tcr"}]]),h=(0,c.A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),x=(0,c.A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),p=(0,c.A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);var g=t(4090),u=t(6695),y=t(8720);let f=[{icon:r.A,title:"Our Address",content:y.r_.address,description:"Located in the heart of Biratnagar for easy access"},{icon:n.A,title:"Phone Numbers",content:y.r_.phone,description:"Emergency: ".concat(y.r_.emergencyPhone),action:"tel:".concat(y.r_.phone)},{icon:l.A,title:"Email Address",content:y.r_.email,description:"We respond within 24 hours",action:"mailto:".concat(y.r_.email)},{icon:o.A,title:"Business Hours",content:"Monday - Friday: 10:00 AM - 6:00 PM",description:"Sunday: 10:00 AM - 6:00 PM | Saturday: Closed"}],b=[{icon:d,title:"By Car",description:"Free parking available for patients"},{icon:m,title:"Public Transport",description:"Accessible by local buses and rickshaws"}];function v(){return(0,i.jsxs)(g.w,{background:"white",children:[(0,i.jsx)(g.X,{subtitle:"Contact Information",title:"How to Reach Us",description:"Find all the information you need to contact us, visit our clinic, or get emergency dental care."}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16",children:f.map((e,a)=>(0,i.jsx)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*a},viewport:{once:!0},children:(0,i.jsx)(u.Zp,{className:"h-full hover:shadow-lg transition-shadow duration-300",children:(0,i.jsx)(u.Wu,{className:"p-6",children:(0,i.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,i.jsx)(e.icon,{className:"h-6 w-6 text-primary"})}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.title}),e.action?(0,i.jsx)("a",{href:e.action,className:"text-primary font-semibold hover:underline block mb-2",children:e.content}):(0,i.jsx)("p",{className:"text-gray-900 font-semibold mb-2",children:e.content}),(0,i.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]})]})})})},e.title))}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,i.jsxs)(s.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Getting to Our Clinic"}),(0,i.jsx)("div",{className:"space-y-4",children:b.map(e=>(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("div",{className:"w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center",children:(0,i.jsx)(e.icon,{className:"h-5 w-5 text-primary"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-900",children:e.title}),(0,i.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]})]},e.title))}),(0,i.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Landmark:"}),(0,i.jsx)("p",{className:"text-gray-600 text-sm",children:"Near Main Chowk, opposite to City Mall. Look for our blue and white signboard."})]})]}),(0,i.jsxs)(s.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Connect With Us"}),(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-900 mb-4",children:"Follow Us"}),(0,i.jsxs)("div",{className:"flex space-x-4",children:[(0,i.jsx)("a",{href:y.r_.socialMedia.facebook,className:"w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center text-white hover:bg-blue-700 transition-colors","aria-label":"Facebook",children:(0,i.jsx)(h,{className:"h-6 w-6"})}),(0,i.jsx)("a",{href:y.r_.socialMedia.instagram,className:"w-12 h-12 bg-pink-600 rounded-lg flex items-center justify-center text-white hover:bg-pink-700 transition-colors","aria-label":"Instagram",children:(0,i.jsx)(x,{className:"h-6 w-6"})}),(0,i.jsx)("a",{href:y.r_.socialMedia.twitter,className:"w-12 h-12 bg-blue-400 rounded-lg flex items-center justify-center text-white hover:bg-blue-500 transition-colors","aria-label":"Twitter",children:(0,i.jsx)(p,{className:"h-6 w-6"})})]})]}),(0,i.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,i.jsx)("h4",{className:"font-semibold text-red-900 mb-2",children:"Emergency Care"}),(0,i.jsx)("p",{className:"text-red-800 text-sm mb-2",children:"For dental emergencies outside business hours, call our emergency line:"}),(0,i.jsx)("a",{href:"tel:".concat(y.r_.emergencyPhone),className:"text-red-600 font-bold text-lg hover:underline",children:y.r_.emergencyPhone}),(0,i.jsx)("p",{className:"text-red-700 text-xs mt-2",children:"Available 24/7 for urgent dental care needs"})]})]})]})]})}},4090:(e,a,t)=>{"use strict";t.d(a,{X:()=>n,w:()=>r});var i=t(5155),s=t(9434);function r(e){let{children:a,className:t,id:r,background:n="white"}=e;return(0,i.jsx)("section",{id:r,className:(0,s.cn)("py-16 sm:py-20",{white:"bg-white",gray:"bg-gray-50",primary:"bg-primary text-primary-foreground"}[n],t),children:(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a})})}function n(e){let{title:a,subtitle:t,description:r,centered:n=!0,className:l}=e;return(0,i.jsxs)("div",{className:(0,s.cn)("mb-12",n&&"text-center",l),children:[t&&(0,i.jsx)("p",{className:"text-primary font-semibold text-sm uppercase tracking-wide mb-2",children:t}),(0,i.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:a}),r&&(0,i.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:r})]})}},4186:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4516:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5277:(e,a,t)=>{"use strict";t.d(a,{default:()=>x});var i=t(5155),s=t(2605),r=t(9420),n=t(8883),l=t(4516),o=t(4186),c=t(4090),d=t(285),m=t(8720);let h=[{icon:r.A,title:"Call Us",value:m.r_.phone,description:"Speak with our friendly staff",action:"tel:".concat(m.r_.phone),color:"bg-green-500"},{icon:n.A,title:"Email Us",value:m.r_.email,description:"Send us your questions",action:"mailto:".concat(m.r_.email),color:"bg-blue-500"},{icon:l.A,title:"Visit Us",value:"Biratnagar, Nepal",description:"Find our clinic location",action:"#location",color:"bg-purple-500"},{icon:o.A,title:"Emergency",value:m.r_.emergencyPhone,description:"24/7 emergency care",action:"tel:".concat(m.r_.emergencyPhone),color:"bg-red-500"}];function x(){return(0,i.jsxs)(c.w,{background:"gray",className:"pt-32 pb-20",children:[(0,i.jsx)("div",{className:"text-center max-w-4xl mx-auto mb-16",children:(0,i.jsxs)(s.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,i.jsx)("p",{className:"text-primary font-semibold text-sm uppercase tracking-wide mb-4",children:"Get In Touch"}),(0,i.jsxs)("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6",children:["Contact"," ",(0,i.jsx)("span",{className:"text-primary",children:"Our Team"})]}),(0,i.jsx)("p",{className:"text-xl text-gray-600 leading-relaxed",children:"Ready to schedule your appointment or have questions about our services? We're here to help and look forward to hearing from you."})]})}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:h.map((e,a)=>(0,i.jsx)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*a},children:(0,i.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center hover:shadow-md transition-shadow duration-300 group",children:[(0,i.jsx)("div",{className:"w-16 h-16 ".concat(e.color," rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300"),children:(0,i.jsx)(e.icon,{className:"h-8 w-8 text-white"})}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.title}),(0,i.jsx)("p",{className:"text-primary font-semibold mb-2",children:e.value}),(0,i.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:e.description}),(0,i.jsx)(d.$,{asChild:!0,variant:"outline",size:"sm",className:"w-full",children:(0,i.jsx)("a",{href:e.action,children:"Contact Now"})})]})},e.title))}),(0,i.jsx)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"mt-16 text-center",children:(0,i.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100 max-w-2xl mx-auto",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"We're Open Today"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-semibold text-gray-900",children:"Regular Hours:"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Monday - Friday: 10:00 AM - 6:00 PM"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Sunday: 10:00 AM - 6:00 PM"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-semibold text-gray-900",children:"Emergency Care:"}),(0,i.jsx)("p",{className:"text-gray-600",children:"24/7 Emergency Services"}),(0,i.jsx)("p",{className:"text-primary font-semibold",children:m.r_.emergencyPhone})]})]})]})})]})}},6695:(e,a,t)=>{"use strict";t.d(a,{Wu:()=>o,ZB:()=>l,Zp:()=>r,aR:()=>n});var i=t(5155);t(2115);var s=t(9434);function r(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function n(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function l(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",a),...t})}function o(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",a),...t})}},8463:(e,a,t)=>{Promise.resolve().then(t.bind(t,2648)),Promise.resolve().then(t.bind(t,5277)),Promise.resolve().then(t.bind(t,3322)),Promise.resolve().then(t.bind(t,1789))},8720:(e,a,t)=>{"use strict";t.d(a,{$p:()=>s,FF:()=>n,rR:()=>r,r_:()=>l,x0:()=>i,x1:()=>o});let i={name:"Dental Care Clinic Biratnagar",tagline:"Your Smile, Our Priority",mission:"To provide exceptional dental care with compassion, using the latest technology and techniques to ensure every patient receives the highest quality treatment in a comfortable, welcoming environment.",story:"Established in 2015, Dental Care Clinic Biratnagar has been serving the community with dedication and excellence. Our clinic was founded with the vision of making quality dental care accessible to everyone in Biratnagar and surrounding areas.",established:"2015",location:"Biratnagar, Nepal"},s=[{id:"1",name:"Dental Cleaning & Checkups",description:"Comprehensive oral examination and professional teeth cleaning to maintain optimal oral health.",benefits:["Prevents cavities and gum disease","Early detection of dental problems","Fresh breath and clean feeling","Maintains overall oral health"],procedure:"Our dental hygienists perform thorough cleaning using ultrasonic scalers and hand instruments, followed by polishing and fluoride treatment.",image:"/images/services/cleaning.jpg",price:"NPR 1,500 - 2,500"},{id:"2",name:"Teeth Whitening",description:"Professional teeth whitening treatments to brighten your smile and boost your confidence.",benefits:["Removes stains and discoloration","Safe and effective results","Boosts self-confidence","Long-lasting whitening effect"],procedure:"We use professional-grade whitening gel applied with custom trays or in-office laser whitening for immediate results.",image:"/images/services/whitening.jpg",price:"NPR 8,000 - 15,000"},{id:"3",name:"Orthodontics",description:"Comprehensive orthodontic treatment including braces and aligners to straighten teeth and correct bite issues.",benefits:["Straighter, more attractive smile","Improved bite function","Better oral hygiene","Enhanced self-esteem"],procedure:"Treatment begins with comprehensive examination, followed by custom treatment plan using traditional braces or clear aligners.",image:"/images/services/orthodontics.jpg",price:"NPR 50,000 - 150,000"},{id:"4",name:"Dental Implants",description:"Permanent tooth replacement solution using titanium implants for missing teeth.",benefits:["Permanent tooth replacement","Natural look and feel","Preserves jawbone structure","No impact on adjacent teeth"],procedure:"Surgical placement of titanium implant into jawbone, followed by healing period and crown placement.",image:"/images/services/implants.jpg",price:"NPR 80,000 - 120,000"},{id:"5",name:"Root Canal Treatment",description:"Advanced endodontic treatment to save infected or severely damaged teeth.",benefits:["Saves natural tooth","Eliminates pain and infection","Prevents further complications","Cost-effective tooth preservation"],procedure:"Removal of infected pulp, cleaning and disinfection of root canals, followed by filling and crown placement.",image:"/images/services/root-canal.jpg",price:"NPR 8,000 - 15,000"},{id:"6",name:"Cosmetic Dentistry",description:"Aesthetic dental treatments including veneers, bonding, and smile makeovers.",benefits:["Enhanced smile appearance","Improved facial aesthetics","Boosted confidence","Customized treatment plans"],procedure:"Comprehensive smile analysis followed by customized treatment using veneers, bonding, or other cosmetic procedures.",image:"/images/services/cosmetic.jpg",price:"NPR 15,000 - 80,000"}],r=[{id:"1",name:"Sita Rai",city:"Biratnagar",rating:5,review:"Excellent service! Dr. Rajesh performed my dental implant surgery with great care. The staff is very professional and the clinic is very clean. Highly recommended!",service:"Dental Implants",date:"2024-01-15",image:"/images/testimonials/sita.jpg"},{id:"2",name:"Ramesh Gurung",city:"Dharan",rating:5,review:"I had my teeth whitening done here and the results are amazing! Dr. Priya explained everything clearly and the treatment was painless. Very satisfied with the service.",service:"Teeth Whitening",date:"2024-02-20",image:"/images/testimonials/ramesh.jpg"},{id:"3",name:"Maya Shrestha",city:"Biratnagar",rating:5,review:"The orthodontic treatment for my daughter was excellent. Dr. Priya was very patient and gentle with her. The braces were fitted perfectly and the results are wonderful.",service:"Orthodontics",date:"2024-03-10",image:"/images/testimonials/maya.jpg"},{id:"4",name:"Bikash Limbu",city:"Itahari",rating:4,review:"Great experience with root canal treatment. Dr. Amit made sure I was comfortable throughout the procedure. The clinic has modern equipment and very hygienic environment.",service:"Root Canal Treatment",date:"2024-03-25",image:"/images/testimonials/bikash.jpg"},{id:"5",name:"Sunita Karki",city:"Biratnagar",rating:5,review:"Regular dental checkups here have kept my teeth healthy. The staff is friendly and the doctors are very knowledgeable. Best dental clinic in Biratnagar!",service:"Dental Cleaning",date:"2024-04-05",image:"/images/testimonials/sunita.jpg"}],n=[{id:"1",src:"/images/gallery/reception.jpg",alt:"Modern reception area",category:"facility",title:"Reception Area",description:"Comfortable and welcoming reception area"},{id:"2",src:"/images/gallery/treatment-room.jpg",alt:"Treatment room with modern equipment",category:"facility",title:"Treatment Room",description:"State-of-the-art treatment rooms"},{id:"3",src:"/images/gallery/dental-chair.jpg",alt:"Modern dental chair",category:"equipment",title:"Dental Chair",description:"Comfortable modern dental chairs"},{id:"4",src:"/images/gallery/xray-machine.jpg",alt:"Digital X-ray machine",category:"equipment",title:"Digital X-Ray",description:"Advanced digital X-ray technology"},{id:"5",src:"/images/gallery/sterilization.jpg",alt:"Sterilization equipment",category:"equipment",title:"Sterilization Unit",description:"Advanced sterilization equipment"},{id:"6",src:"/images/gallery/team-photo.jpg",alt:"Dental team photo",category:"team",title:"Our Team",description:"Professional dental care team"}],l={address:"Main Road, Biratnagar-10, Morang, Nepal",phone:"+977-21-525678",email:"<EMAIL>",emergencyPhone:"+977-9841234567",businessHours:{Sunday:"10:00 AM - 6:00 PM",Monday:"10:00 AM - 6:00 PM",Tuesday:"10:00 AM - 6:00 PM",Wednesday:"10:00 AM - 6:00 PM",Thursday:"10:00 AM - 6:00 PM",Friday:"10:00 AM - 6:00 PM",Saturday:"Closed"},socialMedia:{facebook:"https://facebook.com/dentalcarebiratnagar",instagram:"https://instagram.com/dentalcarebiratnagar",twitter:"https://twitter.com/dentalcarebiratnagar"},mapEmbedUrl:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3571.5234567890123!2d87.2734567890123!3d26.4567890123456!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjbCsDI3JzI0LjQiTiA4N8KwMTYnMjQuNCJF!5e0!3m2!1sen!2snp!4v1234567890123!5m2!1sen!2snp"},o=[{id:"1",name:"Dr. Rajesh Sharma",role:"Chief Dentist & Founder",qualification:"BDS, MDS (Oral & Maxillofacial Surgery)",experience:"12+ years",bio:"Dr. Rajesh Sharma is a highly experienced oral and maxillofacial surgeon with over 12 years of practice. He founded Dental Care Clinic Biratnagar with the vision of providing world-class dental care to the community.",image:"/images/team/dr-rajesh.jpg",specialties:["Oral Surgery","Dental Implants","Complex Extractions","Facial Trauma"]},{id:"2",name:"Dr. Priya Adhikari",role:"Orthodontist",qualification:"BDS, MDS (Orthodontics)",experience:"8+ years",bio:"Dr. Priya Adhikari specializes in orthodontics and has helped hundreds of patients achieve beautiful, straight smiles. She is known for her gentle approach and attention to detail.",image:"/images/team/dr-priya.jpg",specialties:["Braces","Clear Aligners","Pediatric Orthodontics","Bite Correction"]},{id:"3",name:"Dr. Amit Thapa",role:"Endodontist",qualification:"BDS, MDS (Endodontics)",experience:"6+ years",bio:"Dr. Amit Thapa is our endodontic specialist, focusing on root canal treatments and saving natural teeth. His expertise in pain management ensures comfortable treatment experiences.",image:"/images/team/dr-amit.jpg",specialties:["Root Canal Treatment","Endodontic Surgery","Pain Management","Dental Trauma"]}]},8883:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9074:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9420:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9434:(e,a,t)=>{"use strict";t.d(a,{cn:()=>r});var i=t(2596),s=t(9688);function r(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,s.QP)((0,i.$)(a))}}},e=>{e.O(0,[939,441,964,358],()=>e(e.s=8463)),_N_E=e.O()}]);