'use client';

import { motion } from 'framer-motion';
import { Section } from '@/components/ui/section';
import { clinicInfo } from '@/data/mockData';

export default function AboutHero() {
  return (
    <Section background="gray" className="pt-32 pb-20">
      <div className="text-center max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <p className="text-primary font-semibold text-sm uppercase tracking-wide mb-4">
            About Our Clinic
          </p>
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Caring for Smiles Since{' '}
            <span className="text-primary">{clinicInfo.established}</span>
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed mb-8">
            {clinicInfo.mission}
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="relative w-full h-96 rounded-2xl overflow-hidden shadow-2xl"
        >
          <img
            src="https://images.unsplash.com/photo-**********-2a8555f1a136?w=800&h=400&fit=crop&crop=center"
            alt="Our dental clinic team"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent" />
          <div className="absolute bottom-6 left-6 text-white">
            <p className="text-lg font-semibold">Our Professional Team</p>
            <p className="text-sm opacity-90">Dedicated to your oral health</p>
          </div>
        </motion.div>
      </div>
    </Section>
  );
}
