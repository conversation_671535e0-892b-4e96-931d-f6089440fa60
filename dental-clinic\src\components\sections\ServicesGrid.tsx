'use client';

import { motion } from 'framer-motion';
import { Section, SectionHeader } from '@/components/ui/section';
import ServiceCard from '@/components/ui/service-card';
import { services } from '@/data/mockData';

export default function ServicesGrid() {
  return (
    <Section background="white">
      <SectionHeader
        subtitle="What We Offer"
        title="Our Dental Services"
        description="We provide a comprehensive range of dental services using the latest technology and techniques to ensure the best possible outcomes for our patients."
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {services.map((service, index) => (
          <ServiceCard
            key={service.id}
            service={service}
            index={index}
            showPrice={true}
            showFullDescription={true}
          />
        ))}
      </div>

      {/* Additional Services Note */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="mt-16 text-center"
      >
        <div className="bg-gradient-to-r from-primary/5 to-accent/5 rounded-2xl p-8 border border-primary/10">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Don't See What You Need?
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            We offer many additional specialized dental services not listed here. 
            Contact us to discuss your specific dental needs and we'll be happy to help.
          </p>
          <div className="flex flex-wrap justify-center gap-3 text-sm">
            <span className="bg-white px-4 py-2 rounded-full text-gray-700">Pediatric Dentistry</span>
            <span className="bg-white px-4 py-2 rounded-full text-gray-700">Oral Surgery</span>
            <span className="bg-white px-4 py-2 rounded-full text-gray-700">Periodontics</span>
            <span className="bg-white px-4 py-2 rounded-full text-gray-700">Prosthodontics</span>
            <span className="bg-white px-4 py-2 rounded-full text-gray-700">Emergency Care</span>
            <span className="bg-white px-4 py-2 rounded-full text-gray-700">Dental Crowns</span>
          </div>
        </div>
      </motion.div>
    </Section>
  );
}
