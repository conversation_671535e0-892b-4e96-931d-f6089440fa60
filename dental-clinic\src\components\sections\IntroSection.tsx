'use client';

import { motion } from 'framer-motion';
import { Heart, Shield, Users, Award } from 'lucide-react';
import { Section } from '@/components/ui/section';
import { clinicInfo } from '@/data/mockData';

const features = [
  {
    icon: Heart,
    title: 'Compassionate Care',
    description: 'We treat every patient with kindness, understanding, and respect, ensuring a comfortable experience.'
  },
  {
    icon: Shield,
    title: 'Advanced Technology',
    description: 'State-of-the-art equipment and modern techniques for precise, effective dental treatments.'
  },
  {
    icon: Users,
    title: 'Expert Team',
    description: 'Highly qualified dentists and staff with years of experience in comprehensive dental care.'
  },
  {
    icon: Award,
    title: 'Quality Assurance',
    description: 'Committed to maintaining the highest standards of dental care and patient satisfaction.'
  }
];

export default function IntroSection() {
  return (
    <Section background="white">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
        {/* Content */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="space-y-8"
        >
          <div className="space-y-4">
            <p className="text-primary font-semibold text-sm uppercase tracking-wide">
              About Our Clinic
            </p>
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900">
              Dedicated to Your{' '}
              <span className="text-primary">Oral Health</span>
            </h2>
            <p className="text-lg text-gray-600 leading-relaxed">
              {clinicInfo.mission}
            </p>
            <p className="text-gray-600 leading-relaxed">
              {clinicInfo.story}
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="text-3xl font-bold text-primary mb-2">5000+</div>
              <div className="text-sm text-gray-600">Happy Patients</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="text-3xl font-bold text-primary mb-2">9+</div>
              <div className="text-sm text-gray-600">Years Experience</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="text-3xl font-bold text-primary mb-2">15+</div>
              <div className="text-sm text-gray-600">Services Offered</div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="text-3xl font-bold text-primary mb-2">98%</div>
              <div className="text-sm text-gray-600">Satisfaction Rate</div>
            </motion.div>
          </div>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 sm:grid-cols-2 gap-6"
        >
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300"
            >
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                <feature.icon className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </Section>
  );
}
