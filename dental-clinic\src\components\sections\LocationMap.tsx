'use client';

import { motion } from 'framer-motion';
import { MapPin, Navigation, Clock, Phone } from 'lucide-react';
import { Section, SectionHeader } from '@/components/ui/section';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { contactInfo } from '@/data/mockData';

const nearbyLandmarks = [
  'City Mall - 2 minutes walk',
  'Main Chowk - 1 minute walk',
  'Biratnagar Hospital - 5 minutes drive',
  'Bus Station - 10 minutes walk'
];

export default function LocationMap() {
  return (
    <Section background="white" id="location">
      <SectionHeader
        subtitle="Our Location"
        title="Find Us in Biratnagar"
        description="We're conveniently located in the heart of Biratnagar, easily accessible by public transport with parking available for patients."
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Map */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="lg:col-span-2"
        >
          <Card className="overflow-hidden">
            <div className="h-96 bg-gray-200 relative">
              <iframe
                src={contactInfo.mapEmbedUrl}
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="Dental Care Clinic Biratnagar Location"
                className="absolute inset-0"
              />
            </div>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    Dental Care Clinic Biratnagar
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {contactInfo.address}
                  </p>
                </div>
                <Button
                  asChild
                  variant="outline"
                  size="sm"
                >
                  <a
                    href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(contactInfo.address)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Navigation className="h-4 w-4 mr-2" />
                    Get Directions
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Location Details */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="space-y-6"
        >
          {/* Quick Contact */}
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Quick Contact
              </h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <MapPin className="h-5 w-5 text-primary flex-shrink-0" />
                  <p className="text-gray-600 text-sm">{contactInfo.address}</p>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className="h-5 w-5 text-primary flex-shrink-0" />
                  <a
                    href={`tel:${contactInfo.phone}`}
                    className="text-primary font-semibold text-sm hover:underline"
                  >
                    {contactInfo.phone}
                  </a>
                </div>
                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-primary flex-shrink-0" />
                  <div className="text-gray-600 text-sm">
                    <p>Mon-Fri: 10:00 AM - 6:00 PM</p>
                    <p>Sun: 10:00 AM - 6:00 PM</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Nearby Landmarks */}
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Nearby Landmarks
              </h3>
              <div className="space-y-2">
                {nearbyLandmarks.map((landmark, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0" />
                    <p className="text-gray-600 text-sm">{landmark}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Parking Info */}
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Parking & Access
              </h3>
              <div className="space-y-3 text-sm text-gray-600">
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                  <p>Free parking available for patients</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                  <p>Wheelchair accessible entrance</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                  <p>Ground floor location</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                  <p>Public transport accessible</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Call to Action */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="mt-16 text-center"
      >
        <div className="bg-gradient-to-r from-primary to-accent text-white rounded-2xl p-8">
          <h3 className="text-2xl font-bold mb-4">
            Ready to Visit Us?
          </h3>
          <p className="text-xl opacity-90 mb-6">
            Schedule your appointment today and experience exceptional dental care 
            in the heart of Biratnagar.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              asChild
              size="lg"
              variant="secondary"
              className="text-lg px-8 py-6"
            >
              <a href={`tel:${contactInfo.phone}`}>
                Call Now: {contactInfo.phone}
              </a>
            </Button>
            <Button
              asChild
              size="lg"
              variant="outline"
              className="text-lg px-8 py-6 border-white text-white hover:bg-white hover:text-primary"
            >
              <a href="#contact-form">
                Book Online
              </a>
            </Button>
          </div>
        </div>
      </motion.div>
    </Section>
  );
}
