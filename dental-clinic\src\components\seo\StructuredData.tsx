import { clinicInfo, contactInfo, services, teamMembers } from '@/data/mockData';

export function LocalBusinessStructuredData() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Dentist",
    "name": clinicInfo.name,
    "description": clinicInfo.mission,
    "url": "https://dentalcarebiratnagar.com",
    "telephone": contactInfo.phone,
    "email": contactInfo.email,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": contactInfo.address,
      "addressLocality": "Biratnagar",
      "addressRegion": "Morang",
      "addressCountry": "Nepal"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "26.4567890123456",
      "longitude": "87.2734567890123"
    },
    "openingHours": [
      "Mo-Fr 10:00-18:00",
      "Su 10:00-18:00"
    ],
    "priceRange": "$$",
    "image": "https://dentalcarebiratnagar.com/images/clinic-exterior.jpg",
    "sameAs": [
      contactInfo.socialMedia.facebook,
      contactInfo.socialMedia.instagram,
      contactInfo.socialMedia.twitter
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Dental Services",
      "itemListElement": services.map(service => ({
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": service.name,
          "description": service.description
        }
      }))
    },
    "employee": teamMembers.map(member => ({
      "@type": "Person",
      "name": member.name,
      "jobTitle": member.role,
      "description": member.bio
    })),
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": "500",
      "bestRating": "5",
      "worstRating": "1"
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

export function MedicalOrganizationStructuredData() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "MedicalOrganization",
    "name": clinicInfo.name,
    "description": clinicInfo.mission,
    "url": "https://dentalcarebiratnagar.com",
    "telephone": contactInfo.phone,
    "email": contactInfo.email,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": contactInfo.address,
      "addressLocality": "Biratnagar",
      "addressRegion": "Morang",
      "addressCountry": "Nepal"
    },
    "medicalSpecialty": [
      "Dentistry",
      "Orthodontics",
      "Endodontics",
      "Oral Surgery",
      "Cosmetic Dentistry"
    ],
    "availableService": services.map(service => ({
      "@type": "MedicalProcedure",
      "name": service.name,
      "description": service.description
    }))
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}
