'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowR<PERSON>, Star } from 'lucide-react';
import { Section, SectionHeader } from '@/components/ui/section';
import { Button } from '@/components/ui/button';
import TestimonialCard from '@/components/ui/testimonial-card';
import { testimonials } from '@/data/mockData';

export default function TestimonialsSection() {
  // Show only the first 3 testimonials on homepage
  const featuredTestimonials = testimonials.slice(0, 3);

  return (
    <Section background="white">
      <SectionHeader
        subtitle="Patient Reviews"
        title="What Our Patients Say"
        description="Don&apos;t just take our word for it. Here&apos;s what our patients have to say about their experience at our clinic."
      />

      {/* Rating Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="text-center mb-12"
      >
        <div className="inline-flex items-center space-x-2 bg-yellow-50 px-6 py-3 rounded-full">
          <div className="flex space-x-1">
            {[1, 2, 3, 4, 5].map((star) => (
              <Star key={star} className="h-5 w-5 text-yellow-400 fill-current" />
            ))}
          </div>
          <span className="text-lg font-semibold text-gray-900">4.9/5</span>
          <span className="text-gray-600">from 500+ reviews</span>
        </div>
      </motion.div>

      {/* Testimonials Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
        {featuredTestimonials.map((testimonial, index) => (
          <TestimonialCard
            key={testimonial.id}
            testimonial={testimonial}
            index={index}
            showService={true}
          />
        ))}
      </div>

      {/* CTA Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="text-center"
      >
        <div className="bg-gradient-to-r from-primary/5 to-accent/5 rounded-2xl p-8 border border-primary/10">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Join Our Happy Patients
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Experience the same exceptional care that our patients rave about. 
            Book your appointment today and see why we&apos;re Biratnagar&apos;s trusted dental clinic.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/contact">Book Your Appointment</Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link href="/testimonials">
                Read More Reviews
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </motion.div>
    </Section>
  );
}
