import { Metadata } from 'next';
import ServicesHero from '@/components/sections/ServicesHero';
import ServicesGrid from '@/components/sections/ServicesGrid';
import ServiceProcess from '@/components/sections/ServiceProcess';
import ServicesCTA from '@/components/sections/ServicesCTA';

export const metadata: Metadata = {
  title: 'Dental Services | Dental Care Clinic Biratnagar - Complete Oral Care',
  description: 'Comprehensive dental services in Biratnagar including teeth cleaning, whitening, orthodontics, implants, root canal treatment, and cosmetic dentistry.',
  keywords: 'dental services Biratnagar, teeth cleaning, teeth whitening, orthodontics, dental implants, root canal, cosmetic dentistry',
};

export default function ServicesPage() {
  return (
    <>
      <ServicesHero />
      <ServicesGrid />
      <ServiceProcess />
      <ServicesCTA />
    </>
  );
}
