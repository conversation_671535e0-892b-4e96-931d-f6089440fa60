(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{285:(e,a,t)=>{"use strict";t.d(a,{$:()=>l});var i=t(5155);t(2115);var n=t(4624),r=t(2085),s=t(9434);let o=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:a,variant:t,size:r,asChild:l=!1,...c}=e,d=l?n.DX:"button";return(0,i.jsx)(d,{"data-slot":"button",className:(0,s.cn)(o({variant:t,size:r,className:a})),...c})}},347:()=>{},421:(e,a,t)=>{Promise.resolve().then(t.t.bind(t,6874,23)),Promise.resolve().then(t.t.bind(t,1264,23)),Promise.resolve().then(t.t.bind(t,347,23)),Promise.resolve().then(t.bind(t,7035))},1264:e=>{e.exports={style:{fontFamily:"'Poppins', 'Poppins Fallback'",fontStyle:"normal"},className:"__className_51684b",variable:"__variable_51684b"}},4416:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4516:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},7035:(e,a,t)=>{"use strict";t.d(a,{default:()=>u});var i=t(5155),n=t(2115),r=t(6874),s=t.n(r),o=t(2605),l=t(9420),c=t(4516),d=t(4416);let m=(0,t(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]);var h=t(285),g=t(8720);let p=[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Services",href:"/services"},{name:"Gallery",href:"/gallery"},{name:"Testimonials",href:"/testimonials"},{name:"Contact",href:"/contact"}];function u(){let[e,a]=(0,n.useState)(!1);return(0,i.jsxs)("header",{className:"bg-white shadow-sm sticky top-0 z-50",children:[(0,i.jsx)("div",{className:"bg-primary text-primary-foreground py-2",children:(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,i.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,i.jsx)(l.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:g.r_.phone})]}),(0,i.jsxs)("div",{className:"hidden sm:flex items-center space-x-1",children:[(0,i.jsx)(c.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:g.r_.address})]})]}),(0,i.jsx)("div",{className:"hidden md:block",children:(0,i.jsxs)("span",{children:["Emergency: ",g.r_.emergencyPhone]})})]})})}),(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,i.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,i.jsx)("div",{className:"flex items-center",children:(0,i.jsxs)(s(),{href:"/",className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-10 h-10 bg-primary rounded-full flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-primary-foreground font-bold text-lg",children:"DC"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:g.x0.name}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:g.x0.tagline})]})]})}),(0,i.jsx)("nav",{className:"hidden md:flex space-x-8",children:p.map(e=>(0,i.jsx)(s(),{href:e.href,className:"text-gray-700 hover:text-primary transition-colors duration-200 font-medium",children:e.name},e.name))}),(0,i.jsx)("div",{className:"hidden md:block",children:(0,i.jsx)(h.$,{asChild:!0,children:(0,i.jsx)(s(),{href:"/contact",children:"Book Appointment"})})}),(0,i.jsx)("div",{className:"md:hidden",children:(0,i.jsx)(h.$,{variant:"ghost",size:"sm",onClick:()=>a(!e),children:e?(0,i.jsx)(d.A,{className:"h-6 w-6"}):(0,i.jsx)(m,{className:"h-6 w-6"})})})]})}),e&&(0,i.jsx)(o.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"md:hidden bg-white border-t",children:(0,i.jsxs)("div",{className:"px-4 py-2 space-y-1",children:[p.map(e=>(0,i.jsx)(s(),{href:e.href,className:"block px-3 py-2 text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md transition-colors duration-200",onClick:()=>a(!1),children:e.name},e.name)),(0,i.jsx)("div",{className:"pt-2",children:(0,i.jsx)(h.$,{asChild:!0,className:"w-full",children:(0,i.jsx)(s(),{href:"/contact",onClick:()=>a(!1),children:"Book Appointment"})})})]})})]})}},8720:(e,a,t)=>{"use strict";t.d(a,{$p:()=>n,FF:()=>s,rR:()=>r,r_:()=>o,x0:()=>i,x1:()=>l});let i={name:"Dental Care Clinic Biratnagar",tagline:"Your Smile, Our Priority",mission:"To provide exceptional dental care with compassion, using the latest technology and techniques to ensure every patient receives the highest quality treatment in a comfortable, welcoming environment.",story:"Established in 2015, Dental Care Clinic Biratnagar has been serving the community with dedication and excellence. Our clinic was founded with the vision of making quality dental care accessible to everyone in Biratnagar and surrounding areas.",established:"2015",location:"Biratnagar, Nepal"},n=[{id:"1",name:"Dental Cleaning & Checkups",description:"Comprehensive oral examination and professional teeth cleaning to maintain optimal oral health.",benefits:["Prevents cavities and gum disease","Early detection of dental problems","Fresh breath and clean feeling","Maintains overall oral health"],procedure:"Our dental hygienists perform thorough cleaning using ultrasonic scalers and hand instruments, followed by polishing and fluoride treatment.",image:"/images/services/cleaning.jpg",price:"NPR 1,500 - 2,500"},{id:"2",name:"Teeth Whitening",description:"Professional teeth whitening treatments to brighten your smile and boost your confidence.",benefits:["Removes stains and discoloration","Safe and effective results","Boosts self-confidence","Long-lasting whitening effect"],procedure:"We use professional-grade whitening gel applied with custom trays or in-office laser whitening for immediate results.",image:"/images/services/whitening.jpg",price:"NPR 8,000 - 15,000"},{id:"3",name:"Orthodontics",description:"Comprehensive orthodontic treatment including braces and aligners to straighten teeth and correct bite issues.",benefits:["Straighter, more attractive smile","Improved bite function","Better oral hygiene","Enhanced self-esteem"],procedure:"Treatment begins with comprehensive examination, followed by custom treatment plan using traditional braces or clear aligners.",image:"/images/services/orthodontics.jpg",price:"NPR 50,000 - 150,000"},{id:"4",name:"Dental Implants",description:"Permanent tooth replacement solution using titanium implants for missing teeth.",benefits:["Permanent tooth replacement","Natural look and feel","Preserves jawbone structure","No impact on adjacent teeth"],procedure:"Surgical placement of titanium implant into jawbone, followed by healing period and crown placement.",image:"/images/services/implants.jpg",price:"NPR 80,000 - 120,000"},{id:"5",name:"Root Canal Treatment",description:"Advanced endodontic treatment to save infected or severely damaged teeth.",benefits:["Saves natural tooth","Eliminates pain and infection","Prevents further complications","Cost-effective tooth preservation"],procedure:"Removal of infected pulp, cleaning and disinfection of root canals, followed by filling and crown placement.",image:"/images/services/root-canal.jpg",price:"NPR 8,000 - 15,000"},{id:"6",name:"Cosmetic Dentistry",description:"Aesthetic dental treatments including veneers, bonding, and smile makeovers.",benefits:["Enhanced smile appearance","Improved facial aesthetics","Boosted confidence","Customized treatment plans"],procedure:"Comprehensive smile analysis followed by customized treatment using veneers, bonding, or other cosmetic procedures.",image:"/images/services/cosmetic.jpg",price:"NPR 15,000 - 80,000"}],r=[{id:"1",name:"Sita Rai",city:"Biratnagar",rating:5,review:"Excellent service! Dr. Rajesh performed my dental implant surgery with great care. The staff is very professional and the clinic is very clean. Highly recommended!",service:"Dental Implants",date:"2024-01-15",image:"/images/testimonials/sita.jpg"},{id:"2",name:"Ramesh Gurung",city:"Dharan",rating:5,review:"I had my teeth whitening done here and the results are amazing! Dr. Priya explained everything clearly and the treatment was painless. Very satisfied with the service.",service:"Teeth Whitening",date:"2024-02-20",image:"/images/testimonials/ramesh.jpg"},{id:"3",name:"Maya Shrestha",city:"Biratnagar",rating:5,review:"The orthodontic treatment for my daughter was excellent. Dr. Priya was very patient and gentle with her. The braces were fitted perfectly and the results are wonderful.",service:"Orthodontics",date:"2024-03-10",image:"/images/testimonials/maya.jpg"},{id:"4",name:"Bikash Limbu",city:"Itahari",rating:4,review:"Great experience with root canal treatment. Dr. Amit made sure I was comfortable throughout the procedure. The clinic has modern equipment and very hygienic environment.",service:"Root Canal Treatment",date:"2024-03-25",image:"/images/testimonials/bikash.jpg"},{id:"5",name:"Sunita Karki",city:"Biratnagar",rating:5,review:"Regular dental checkups here have kept my teeth healthy. The staff is friendly and the doctors are very knowledgeable. Best dental clinic in Biratnagar!",service:"Dental Cleaning",date:"2024-04-05",image:"/images/testimonials/sunita.jpg"}],s=[{id:"1",src:"/images/gallery/reception.jpg",alt:"Modern reception area",category:"facility",title:"Reception Area",description:"Comfortable and welcoming reception area"},{id:"2",src:"/images/gallery/treatment-room.jpg",alt:"Treatment room with modern equipment",category:"facility",title:"Treatment Room",description:"State-of-the-art treatment rooms"},{id:"3",src:"/images/gallery/dental-chair.jpg",alt:"Modern dental chair",category:"equipment",title:"Dental Chair",description:"Comfortable modern dental chairs"},{id:"4",src:"/images/gallery/xray-machine.jpg",alt:"Digital X-ray machine",category:"equipment",title:"Digital X-Ray",description:"Advanced digital X-ray technology"},{id:"5",src:"/images/gallery/sterilization.jpg",alt:"Sterilization equipment",category:"equipment",title:"Sterilization Unit",description:"Advanced sterilization equipment"},{id:"6",src:"/images/gallery/team-photo.jpg",alt:"Dental team photo",category:"team",title:"Our Team",description:"Professional dental care team"}],o={address:"Main Road, Biratnagar-10, Morang, Nepal",phone:"+977-21-525678",email:"<EMAIL>",emergencyPhone:"+977-9841234567",businessHours:{Sunday:"10:00 AM - 6:00 PM",Monday:"10:00 AM - 6:00 PM",Tuesday:"10:00 AM - 6:00 PM",Wednesday:"10:00 AM - 6:00 PM",Thursday:"10:00 AM - 6:00 PM",Friday:"10:00 AM - 6:00 PM",Saturday:"Closed"},socialMedia:{facebook:"https://facebook.com/dentalcarebiratnagar",instagram:"https://instagram.com/dentalcarebiratnagar",twitter:"https://twitter.com/dentalcarebiratnagar"},mapEmbedUrl:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3571.5234567890123!2d87.2734567890123!3d26.4567890123456!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjbCsDI3JzI0LjQiTiA4N8KwMTYnMjQuNCJF!5e0!3m2!1sen!2snp!4v1234567890123!5m2!1sen!2snp"},l=[{id:"1",name:"Dr. Rajesh Sharma",role:"Chief Dentist & Founder",qualification:"BDS, MDS (Oral & Maxillofacial Surgery)",experience:"12+ years",bio:"Dr. Rajesh Sharma is a highly experienced oral and maxillofacial surgeon with over 12 years of practice. He founded Dental Care Clinic Biratnagar with the vision of providing world-class dental care to the community.",image:"/images/team/dr-rajesh.jpg",specialties:["Oral Surgery","Dental Implants","Complex Extractions","Facial Trauma"]},{id:"2",name:"Dr. Priya Adhikari",role:"Orthodontist",qualification:"BDS, MDS (Orthodontics)",experience:"8+ years",bio:"Dr. Priya Adhikari specializes in orthodontics and has helped hundreds of patients achieve beautiful, straight smiles. She is known for her gentle approach and attention to detail.",image:"/images/team/dr-priya.jpg",specialties:["Braces","Clear Aligners","Pediatric Orthodontics","Bite Correction"]},{id:"3",name:"Dr. Amit Thapa",role:"Endodontist",qualification:"BDS, MDS (Endodontics)",experience:"6+ years",bio:"Dr. Amit Thapa is our endodontic specialist, focusing on root canal treatments and saving natural teeth. His expertise in pain management ensures comfortable treatment experiences.",image:"/images/team/dr-amit.jpg",specialties:["Root Canal Treatment","Endodontic Surgery","Pain Management","Dental Trauma"]}]},9420:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});let i=(0,t(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9434:(e,a,t)=>{"use strict";t.d(a,{cn:()=>r});var i=t(2596),n=t(9688);function r(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,n.QP)((0,i.$)(a))}}},e=>{e.O(0,[418,939,874,441,964,358],()=>e(e.s=421)),_N_E=e.O()}]);