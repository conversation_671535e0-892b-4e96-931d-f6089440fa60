(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[759],{224:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(9946).A)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]])},1637:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var i=a(5155),s=a(2605),r=a(8564),l=a(224),n=a(6695),c=a(6126);function d(e){let t,{testimonial:a,index:d=0,showService:o=!0}=e;return(0,i.jsx)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*d},children:(0,i.jsx)(n.Zp,{className:"h-full hover:shadow-lg transition-shadow duration-300",children:(0,i.jsxs)(n.Wu,{className:"p-6",children:[(0,i.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,i.jsx)(l.A,{className:"h-8 w-8 text-primary opacity-50"}),(0,i.jsx)("div",{className:"flex space-x-1",children:(t=a.rating,Array.from({length:5},(e,a)=>(0,i.jsx)(r.A,{className:"h-4 w-4 ".concat(a<t?"text-yellow-400 fill-current":"text-gray-300")},a)))})]}),(0,i.jsxs)("blockquote",{className:"text-gray-700 mb-6 leading-relaxed",children:['"',a.review,'"']}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-full overflow-hidden",children:(0,i.jsx)("img",{src:a.image||"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",alt:a.name,className:"w-full h-full object-cover",onError:e=>{e.target.src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"}})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-semibold text-gray-900",children:a.name}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:a.city})]})]}),o&&(0,i.jsx)(c.E,{variant:"outline",className:"text-xs",children:a.service})]}),(0,i.jsx)("div",{className:"mt-4 text-xs text-gray-500",children:new Date(a.date).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]})})})}},2203:(e,t,a)=>{"use strict";a.d(t,{default:()=>g});var i=a(5155),s=a(2605),r=a(6874),l=a.n(r),n=a(8564),c=a(7580);let d=(0,a(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var o=a(9037),x=a(2138),m=a(4090),h=a(285),p=a(6695);let u=[{icon:n.A,title:"Service Quality",percentage:98,description:"Patients rate our service quality as excellent"},{icon:c.A,title:"Staff Friendliness",percentage:97,description:"Patients appreciate our caring and friendly staff"},{icon:d,title:"Treatment Effectiveness",percentage:96,description:"Successful treatment outcomes reported"},{icon:o.A,title:"Overall Experience",percentage:95,description:"Would recommend us to friends and family"}],y=[{category:"Most Appreciated",items:["Professional staff","Clean facilities","Pain-free treatments","Clear explanations"]},{category:"Top Services",items:["Teeth cleaning","Dental implants","Orthodontics","Root canal treatment"]},{category:"Patient Demographics",items:["Families with children","Working professionals","Senior citizens","Students"]}];function g(){return(0,i.jsxs)(m.w,{background:"gray",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16",children:[(0,i.jsxs)(s.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:[(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Patient Satisfaction Metrics"}),(0,i.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Our commitment to excellence is reflected in these satisfaction scores based on patient feedback and reviews."})]}),(0,i.jsx)("div",{className:"space-y-6",children:u.map((e,t)=>(0,i.jsxs)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("div",{className:"w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center",children:(0,i.jsx)(e.icon,{className:"h-5 w-5 text-primary"})}),(0,i.jsx)("h3",{className:"font-semibold text-gray-900",children:e.title})]}),(0,i.jsxs)("span",{className:"text-2xl font-bold text-primary",children:[e.percentage,"%"]})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-2",children:(0,i.jsx)(s.P.div,{initial:{width:0},whileInView:{width:"".concat(e.percentage,"%")},transition:{duration:1,delay:.1*t},viewport:{once:!0},className:"bg-primary h-2 rounded-full"})}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]},e.title))})]}),(0,i.jsxs)(s.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:[(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Review Highlights"}),(0,i.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Common themes and highlights from our patient reviews and testimonials."})]}),(0,i.jsx)("div",{className:"space-y-6",children:y.map((e,t)=>(0,i.jsx)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},children:(0,i.jsx)(p.Zp,{className:"hover:shadow-md transition-shadow duration-300",children:(0,i.jsxs)(p.Wu,{className:"p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:e.category}),(0,i.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:e.items.map((e,t)=>(0,i.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full flex-shrink-0"}),(0,i.jsx)("span",{children:e})]},t))})]})})},e.category))})]})]}),(0,i.jsx)(s.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mt-16 text-center",children:(0,i.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Ready to Experience Exceptional Dental Care?"}),(0,i.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Join thousands of satisfied patients who have trusted us with their dental care. Schedule your appointment today and discover why we're Biratnagar's preferred dental clinic."}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,i.jsx)(h.$,{asChild:!0,size:"lg",children:(0,i.jsx)(l(),{href:"/contact",children:"Book Your Appointment"})}),(0,i.jsx)(h.$,{asChild:!0,variant:"outline",size:"lg",children:(0,i.jsxs)(l(),{href:"/services",children:["Explore Our Services",(0,i.jsx)(x.A,{className:"ml-2 h-4 w-4"})]})})]})]})})]})}},3699:(e,t,a)=>{"use strict";a.d(t,{default:()=>x});var i=a(5155),s=a(2605),r=a(8564),l=a(7580),n=a(1976),c=a(224),d=a(4090);let o=[{icon:r.A,title:"Average Rating",value:"4.9/5",description:"Based on 500+ reviews"},{icon:l.A,title:"Happy Patients",value:"5000+",description:"Served since 2015"},{icon:n.A,title:"Satisfaction Rate",value:"98%",description:"Would recommend us"},{icon:c.A,title:"Testimonials",value:"200+",description:"Written reviews"}];function x(){return(0,i.jsxs)(d.w,{background:"gray",className:"pt-32 pb-20",children:[(0,i.jsx)("div",{className:"text-center max-w-4xl mx-auto mb-16",children:(0,i.jsxs)(s.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,i.jsx)("p",{className:"text-primary font-semibold text-sm uppercase tracking-wide mb-4",children:"Patient Testimonials"}),(0,i.jsxs)("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6",children:["What Our"," ",(0,i.jsx)("span",{className:"text-primary",children:"Patients Say"})]}),(0,i.jsx)("p",{className:"text-xl text-gray-600 leading-relaxed mb-8",children:"Don't just take our word for it. Read genuine reviews from our satisfied patients who have experienced our exceptional dental care firsthand."}),(0,i.jsxs)("div",{className:"inline-flex items-center space-x-2 bg-white px-8 py-4 rounded-full shadow-sm border border-gray-100",children:[(0,i.jsx)("div",{className:"flex space-x-1",children:[1,2,3,4,5].map(e=>(0,i.jsx)(r.A,{className:"h-6 w-6 text-yellow-400 fill-current"},e))}),(0,i.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"4.9"}),(0,i.jsx)("span",{className:"text-gray-600",children:"out of 5 stars"})]})]})}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:o.map((e,t)=>(0,i.jsxs)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center hover:shadow-md transition-shadow duration-300",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,i.jsx)(e.icon,{className:"h-8 w-8 text-primary"})}),(0,i.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:e.value}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:e.title}),(0,i.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]},e.title))})]})}},5377:(e,t,a)=>{Promise.resolve().then(a.bind(a,6579)),Promise.resolve().then(a.bind(a,3699)),Promise.resolve().then(a.bind(a,2203))},6579:(e,t,a)=>{"use strict";a.d(t,{default:()=>x});var i=a(5155),s=a(2115),r=a(2605);let l=(0,a(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]);var n=a(4090),c=a(285),d=a(1637),o=a(8720);function x(){let[e,t]=(0,s.useState)("all"),a=["all",...Array.from(new Set(o.rR.map(e=>e.service)))],x="all"===e?o.rR:o.rR.filter(t=>t.service===e);return(0,i.jsxs)(n.w,{background:"white",children:[(0,i.jsx)(n.X,{subtitle:"Patient Reviews",title:"Real Stories from Real Patients",description:"Read authentic testimonials from our patients who have experienced our dental care services. Their stories speak to our commitment to excellence."}),(0,i.jsxs)("div",{className:"flex flex-wrap justify-center gap-3 mb-12",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600 mr-4",children:[(0,i.jsx)(l,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"text-sm font-medium",children:"Filter by service:"})]}),a.map(a=>(0,i.jsx)(c.$,{variant:e===a?"default":"outline",size:"sm",onClick:()=>t(a),children:"all"===a?"All Services":a},a))]}),(0,i.jsx)(r.P.div,{layout:!0,className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:x.map((t,a)=>(0,i.jsx)(d.A,{testimonial:t,index:a,showService:"all"===e},t.id))}),0===x.length&&(0,i.jsxs)(r.P.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-12",children:[(0,i.jsx)("p",{className:"text-gray-600 text-lg",children:"No testimonials found for the selected service."}),(0,i.jsx)(c.$,{variant:"outline",onClick:()=>t("all"),className:"mt-4",children:"Show All Testimonials"})]}),(0,i.jsx)(r.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mt-16 text-center",children:(0,i.jsxs)("div",{className:"bg-gradient-to-r from-primary/5 to-accent/5 rounded-2xl p-8 border border-primary/10",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Share Your Experience"}),(0,i.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Have you been treated at our clinic? We'd love to hear about your experience. Your feedback helps us improve and helps other patients make informed decisions."}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,i.jsx)(c.$,{size:"lg",children:"Leave a Review"}),(0,i.jsx)(c.$,{variant:"outline",size:"lg",children:"Contact Us"})]})]})})]})}},8564:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])}},e=>{e.O(0,[939,874,343,441,964,358],()=>e(e.s=5377)),_N_E=e.O()}]);