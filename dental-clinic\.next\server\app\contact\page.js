(()=>{var a={};a.id=977,a.ids=[977],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},383:(a,b,c)=>{"use strict";c.d(b,{default:()=>u});var d=c(60687),e=c(51743),f=c(97992),g=c(48340),h=c(41550),i=c(48730),j=c(62688);let k=(0,j.A)("car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]]),l=(0,j.A)("bus",[["path",{d:"M8 6v6",key:"18i7km"}],["path",{d:"M15 6v6",key:"1sg6z9"}],["path",{d:"M2 12h19.6",key:"de5uta"}],["path",{d:"M18 18h3s.5-1.7.8-2.8c.1-.4.2-.8.2-1.2 0-.4-.1-.8-.2-1.2l-1.4-5C20.1 6.8 19.1 6 18 6H4a2 2 0 0 0-2 2v10h3",key:"1wwztk"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}],["path",{d:"M9 18h5",key:"lrx6i"}],["circle",{cx:"16",cy:"18",r:"2",key:"1v4tcr"}]]),m=(0,j.A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),n=(0,j.A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),o=(0,j.A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);var p=c(71134),q=c(44493),r=c(83446);let s=[{icon:f.A,title:"Our Address",content:r.r_.address,description:"Located in the heart of Biratnagar for easy access"},{icon:g.A,title:"Phone Numbers",content:r.r_.phone,description:`Emergency: ${r.r_.emergencyPhone}`,action:`tel:${r.r_.phone}`},{icon:h.A,title:"Email Address",content:r.r_.email,description:"We respond within 24 hours",action:`mailto:${r.r_.email}`},{icon:i.A,title:"Business Hours",content:"Monday - Friday: 10:00 AM - 6:00 PM",description:"Sunday: 10:00 AM - 6:00 PM | Saturday: Closed"}],t=[{icon:k,title:"By Car",description:"Free parking available for patients"},{icon:l,title:"Public Transport",description:"Accessible by local buses and rickshaws"}];function u(){return(0,d.jsxs)(p.w,{background:"white",children:[(0,d.jsx)(p.X,{subtitle:"Contact Information",title:"How to Reach Us",description:"Find all the information you need to contact us, visit our clinic, or get emergency dental care."}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16",children:s.map((a,b)=>(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},viewport:{once:!0},children:(0,d.jsx)(q.Zp,{className:"h-full hover:shadow-lg transition-shadow duration-300",children:(0,d.jsx)(q.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,d.jsx)(a.icon,{className:"h-6 w-6 text-primary"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:a.title}),a.action?(0,d.jsx)("a",{href:a.action,className:"text-primary font-semibold hover:underline block mb-2",children:a.content}):(0,d.jsx)("p",{className:"text-gray-900 font-semibold mb-2",children:a.content}),(0,d.jsx)("p",{className:"text-gray-600 text-sm",children:a.description})]})]})})})},a.title))}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Getting to Our Clinic"}),(0,d.jsx)("div",{className:"space-y-4",children:t.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center",children:(0,d.jsx)(a.icon,{className:"h-5 w-5 text-primary"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900",children:a.title}),(0,d.jsx)("p",{className:"text-gray-600 text-sm",children:a.description})]})]},a.title))}),(0,d.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Landmark:"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm",children:"Near Main Chowk, opposite to City Mall. Look for our blue and white signboard."})]})]}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Connect With Us"}),(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-4",children:"Follow Us"}),(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsx)("a",{href:r.r_.socialMedia.facebook,className:"w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center text-white hover:bg-blue-700 transition-colors","aria-label":"Facebook",children:(0,d.jsx)(m,{className:"h-6 w-6"})}),(0,d.jsx)("a",{href:r.r_.socialMedia.instagram,className:"w-12 h-12 bg-pink-600 rounded-lg flex items-center justify-center text-white hover:bg-pink-700 transition-colors","aria-label":"Instagram",children:(0,d.jsx)(n,{className:"h-6 w-6"})}),(0,d.jsx)("a",{href:r.r_.socialMedia.twitter,className:"w-12 h-12 bg-blue-400 rounded-lg flex items-center justify-center text-white hover:bg-blue-500 transition-colors","aria-label":"Twitter",children:(0,d.jsx)(o,{className:"h-6 w-6"})})]})]}),(0,d.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,d.jsx)("h4",{className:"font-semibold text-red-900 mb-2",children:"Emergency Care"}),(0,d.jsx)("p",{className:"text-red-800 text-sm mb-2",children:"For dental emergencies outside business hours, call our emergency line:"}),(0,d.jsx)("a",{href:`tel:${r.r_.emergencyPhone}`,className:"text-red-600 font-bold text-lg hover:underline",children:r.r_.emergencyPhone}),(0,d.jsx)("p",{className:"text-red-700 text-xs mt-2",children:"Available 24/7 for urgent dental care needs"})]})]})]})]})}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21542:(a,b,c)=>{"use strict";c.d(b,{A:()=>p});var d=c(60687),e=c(43210),f=c(51743),g=c(29523),h=c(4780);function i({className:a,type:b,...c}){return(0,d.jsx)("input",{type:b,"data-slot":"input",className:(0,h.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...c})}function j({className:a,...b}){return(0,d.jsx)("textarea",{"data-slot":"textarea",className:(0,h.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...b})}c(51215);var k=c(81391),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,b)=>{let c=(0,k.TL)(`Primitive.${b}`),f=e.forwardRef((a,e)=>{let{asChild:f,...g}=a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(f?c:b,{...g,ref:e})});return f.displayName=`Primitive.${b}`,{...a,[b]:f}},{}),m=e.forwardRef((a,b)=>(0,d.jsx)(l.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));function n({className:a,...b}){return(0,d.jsx)(m,{"data-slot":"label",className:(0,h.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...b})}m.displayName="Label";var o=c(44493);function p({title:a="Send us a Message",showCard:b=!0}){let[c,h]=(0,e.useState)({name:"",email:"",phone:"",subject:"",message:""}),[k,l]=(0,e.useState)(!1),[m,p]=(0,e.useState)(!1),q=a=>{h(b=>({...b,[a.target.name]:a.target.value}))},r=async a=>{a.preventDefault(),l(!0),await new Promise(a=>setTimeout(a,1e3)),l(!1),p(!0),setTimeout(()=>{p(!1),h({name:"",email:"",phone:"",subject:"",message:""})},3e3)},s=()=>(0,d.jsxs)("div",{className:"space-y-6",children:[a&&(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:a}),(0,d.jsx)("p",{className:"text-gray-600",children:"We'd love to hear from you. Send us a message and we'll respond as soon as possible."})]}),m?(0,d.jsxs)(f.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"text-center py-8",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,d.jsx)("h4",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Message Sent!"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Thank you for contacting us. We'll get back to you soon."})]}):(0,d.jsxs)("form",{onSubmit:r,className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n,{htmlFor:"name",children:"Full Name *"}),(0,d.jsx)(i,{id:"name",name:"name",type:"text",required:!0,value:c.name,onChange:q,placeholder:"Your full name"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n,{htmlFor:"email",children:"Email Address *"}),(0,d.jsx)(i,{id:"email",name:"email",type:"email",required:!0,value:c.email,onChange:q,placeholder:"<EMAIL>"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n,{htmlFor:"phone",children:"Phone Number"}),(0,d.jsx)(i,{id:"phone",name:"phone",type:"tel",value:c.phone,onChange:q,placeholder:"+977-XXX-XXXXXX"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n,{htmlFor:"subject",children:"Subject *"}),(0,d.jsx)(i,{id:"subject",name:"subject",type:"text",required:!0,value:c.subject,onChange:q,placeholder:"What is this regarding?"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(n,{htmlFor:"message",children:"Message *"}),(0,d.jsx)(j,{id:"message",name:"message",required:!0,rows:5,value:c.message,onChange:q,placeholder:"Tell us how we can help you..."})]}),(0,d.jsx)(g.$,{type:"submit",className:"w-full",disabled:k,children:k?"Sending...":"Send Message"})]})]});return b?(0,d.jsx)(o.Zp,{children:(0,d.jsx)(o.Wu,{className:"p-6",children:(0,d.jsx)(s,{})})}):(0,d.jsx)(s,{})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27868:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,43839)),"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\app\\contact\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,92302)),"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\app\\contact\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/contact/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32088:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\dental\\\\dental-clinic\\\\src\\\\components\\\\sections\\\\ContactFormSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\components\\sections\\ContactFormSection.tsx","default")},33185:(a,b,c)=>{Promise.resolve().then(c.bind(c,32088)),Promise.resolve().then(c.bind(c,76199)),Promise.resolve().then(c.bind(c,37731)),Promise.resolve().then(c.bind(c,61514))},33873:a=>{"use strict";a.exports=require("path")},37731:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\dental\\\\dental-clinic\\\\src\\\\components\\\\sections\\\\ContactInfo.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\components\\sections\\ContactInfo.tsx","default")},40201:(a,b,c)=>{"use strict";c.d(b,{default:()=>n});var d=c(60687),e=c(51743),f=c(48340),g=c(41550),h=c(97992),i=c(48730),j=c(71134),k=c(29523),l=c(83446);let m=[{icon:f.A,title:"Call Us",value:l.r_.phone,description:"Speak with our friendly staff",action:`tel:${l.r_.phone}`,color:"bg-green-500"},{icon:g.A,title:"Email Us",value:l.r_.email,description:"Send us your questions",action:`mailto:${l.r_.email}`,color:"bg-blue-500"},{icon:h.A,title:"Visit Us",value:"Biratnagar, Nepal",description:"Find our clinic location",action:"#location",color:"bg-purple-500"},{icon:i.A,title:"Emergency",value:l.r_.emergencyPhone,description:"24/7 emergency care",action:`tel:${l.r_.emergencyPhone}`,color:"bg-red-500"}];function n(){return(0,d.jsxs)(j.w,{background:"gray",className:"pt-32 pb-20",children:[(0,d.jsx)("div",{className:"text-center max-w-4xl mx-auto mb-16",children:(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,d.jsx)("p",{className:"text-primary font-semibold text-sm uppercase tracking-wide mb-4",children:"Get In Touch"}),(0,d.jsxs)("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6",children:["Contact"," ",(0,d.jsx)("span",{className:"text-primary",children:"Our Team"})]}),(0,d.jsx)("p",{className:"text-xl text-gray-600 leading-relaxed",children:"Ready to schedule your appointment or have questions about our services? We're here to help and look forward to hearing from you."})]})}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:m.map((a,b)=>(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},children:(0,d.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center hover:shadow-md transition-shadow duration-300 group",children:[(0,d.jsx)("div",{className:`w-16 h-16 ${a.color} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`,children:(0,d.jsx)(a.icon,{className:"h-8 w-8 text-white"})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:a.title}),(0,d.jsx)("p",{className:"text-primary font-semibold mb-2",children:a.value}),(0,d.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:a.description}),(0,d.jsx)(k.$,{asChild:!0,variant:"outline",size:"sm",className:"w-full",children:(0,d.jsx)("a",{href:a.action,children:"Contact Now"})})]})},a.title))}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"mt-16 text-center",children:(0,d.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100 max-w-2xl mx-auto",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"We're Open Today"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-semibold text-gray-900",children:"Regular Hours:"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Monday - Friday: 10:00 AM - 6:00 PM"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Sunday: 10:00 AM - 6:00 PM"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-semibold text-gray-900",children:"Emergency Care:"}),(0,d.jsx)("p",{className:"text-gray-600",children:"24/7 Emergency Services"}),(0,d.jsx)("p",{className:"text-primary font-semibold",children:l.r_.emergencyPhone})]})]})]})})]})}},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41550:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},43839:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(37413),e=c(76199),f=c(37731),g=c(32088),h=c(61514);let i={title:"Contact Us | Dental Care Clinic Biratnagar - Book Appointment",description:"Contact Dental Care Clinic Biratnagar to book your appointment. Find our location, phone number, business hours, and emergency contact information.",keywords:"contact dental clinic Biratnagar, book appointment, dental clinic location, emergency dental care"};function j(){return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(e.default,{}),(0,d.jsx)(f.default,{}),(0,d.jsx)(g.default,{}),(0,d.jsx)(h.default,{})]})}},44493:(a,b,c)=>{"use strict";c.d(b,{Wu:()=>i,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},46337:(a,b,c)=>{Promise.resolve().then(c.bind(c,63319)),Promise.resolve().then(c.bind(c,40201)),Promise.resolve().then(c.bind(c,383)),Promise.resolve().then(c.bind(c,48113))},48113:(a,b,c)=>{"use strict";c.d(b,{default:()=>o});var d=c(60687),e=c(51743);let f=(0,c(62688).A)("navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]]);var g=c(97992),h=c(48340),i=c(48730),j=c(71134),k=c(29523),l=c(44493),m=c(83446);let n=["City Mall - 2 minutes walk","Main Chowk - 1 minute walk","Biratnagar Hospital - 5 minutes drive","Bus Station - 10 minutes walk"];function o(){return(0,d.jsxs)(j.w,{background:"white",id:"location",children:[(0,d.jsx)(j.X,{subtitle:"Our Location",title:"Find Us in Biratnagar",description:"We're conveniently located in the heart of Biratnagar, easily accessible by public transport with parking available for patients."}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,d.jsx)(e.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"lg:col-span-2",children:(0,d.jsxs)(l.Zp,{className:"overflow-hidden",children:[(0,d.jsx)("div",{className:"h-96 bg-gray-200 relative",children:(0,d.jsx)("iframe",{src:m.r_.mapEmbedUrl,width:"100%",height:"100%",style:{border:0},allowFullScreen:!0,loading:"lazy",referrerPolicy:"no-referrer-when-downgrade",title:"Dental Care Clinic Biratnagar Location",className:"absolute inset-0"})}),(0,d.jsx)(l.Wu,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:"Dental Care Clinic Biratnagar"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm",children:m.r_.address})]}),(0,d.jsx)(k.$,{asChild:!0,variant:"outline",size:"sm",children:(0,d.jsxs)("a",{href:`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(m.r_.address)}`,target:"_blank",rel:"noopener noreferrer",children:[(0,d.jsx)(f,{className:"h-4 w-4 mr-2"}),"Get Directions"]})})]})})]})}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-6",children:[(0,d.jsx)(l.Zp,{children:(0,d.jsxs)(l.Wu,{className:"p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Contact"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(g.A,{className:"h-5 w-5 text-primary flex-shrink-0"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm",children:m.r_.address})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(h.A,{className:"h-5 w-5 text-primary flex-shrink-0"}),(0,d.jsx)("a",{href:`tel:${m.r_.phone}`,className:"text-primary font-semibold text-sm hover:underline",children:m.r_.phone})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(i.A,{className:"h-5 w-5 text-primary flex-shrink-0"}),(0,d.jsxs)("div",{className:"text-gray-600 text-sm",children:[(0,d.jsx)("p",{children:"Mon-Fri: 10:00 AM - 6:00 PM"}),(0,d.jsx)("p",{children:"Sun: 10:00 AM - 6:00 PM"})]})]})]})]})}),(0,d.jsx)(l.Zp,{children:(0,d.jsxs)(l.Wu,{className:"p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Nearby Landmarks"}),(0,d.jsx)("div",{className:"space-y-2",children:n.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full flex-shrink-0"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm",children:a})]},b))})]})}),(0,d.jsx)(l.Zp,{children:(0,d.jsxs)(l.Wu,{className:"p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Parking & Access"}),(0,d.jsxs)("div",{className:"space-y-3 text-sm text-gray-600",children:[(0,d.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"}),(0,d.jsx)("p",{children:"Free parking available for patients"})]}),(0,d.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"}),(0,d.jsx)("p",{children:"Wheelchair accessible entrance"})]}),(0,d.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"}),(0,d.jsx)("p",{children:"Ground floor location"})]}),(0,d.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"}),(0,d.jsx)("p",{children:"Public transport accessible"})]})]})]})})]})]}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mt-16 text-center",children:(0,d.jsxs)("div",{className:"bg-gradient-to-r from-primary to-accent text-white rounded-2xl p-8",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold mb-4",children:"Ready to Visit Us?"}),(0,d.jsx)("p",{className:"text-xl opacity-90 mb-6",children:"Schedule your appointment today and experience exceptional dental care in the heart of Biratnagar."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(k.$,{asChild:!0,size:"lg",variant:"secondary",className:"text-lg px-8 py-6",children:(0,d.jsxs)("a",{href:`tel:${m.r_.phone}`,children:["Call Now: ",m.r_.phone]})}),(0,d.jsx)(k.$,{asChild:!0,size:"lg",variant:"outline",className:"text-lg px-8 py-6 border-white text-white hover:bg-white hover:text-primary",children:(0,d.jsx)("a",{href:"#contact-form",children:"Book Online"})})]})]})})]})}},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},61514:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\dental\\\\dental-clinic\\\\src\\\\components\\\\sections\\\\LocationMap.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\components\\sections\\LocationMap.tsx","default")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63319:(a,b,c)=>{"use strict";c.d(b,{default:()=>m});var d=c(60687),e=c(51743);let f=(0,c(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);var g=c(48340),h=c(41550),i=c(40228),j=c(71134),k=c(21542);let l=[{icon:f,title:"Online Form",description:"Fill out our contact form and we'll get back to you within 24 hours",highlight:"Most Popular"},{icon:g.A,title:"Phone Call",description:"Speak directly with our staff to schedule your appointment",highlight:"Immediate Response"},{icon:h.A,title:"Email",description:"Send us detailed questions about our services or your dental needs",highlight:"Detailed Inquiries"},{icon:i.A,title:"Walk-in",description:"Visit our clinic during business hours for immediate assistance",highlight:"Emergency Cases"}];function m(){return(0,d.jsxs)(j.w,{background:"gray",children:[(0,d.jsx)(j.X,{subtitle:"Get In Touch",title:"Send Us a Message",description:"Choose the best way to contact us. We're here to answer your questions and help you schedule your dental care."}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-8",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Ways to Contact Us"}),(0,d.jsx)("p",{className:"text-gray-600 mb-8",children:"We offer multiple convenient ways to get in touch with our dental team. Choose the method that works best for you."})]}),(0,d.jsx)("div",{className:"space-y-6",children:l.map((a,b)=>(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},viewport:{once:!0},className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300",children:(0,d.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,d.jsx)(a.icon,{className:"h-6 w-6 text-primary"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,d.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:a.title}),(0,d.jsx)("span",{className:"text-xs bg-primary/10 text-primary px-2 py-1 rounded-full",children:a.highlight})]}),(0,d.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:a.description})]})]})},a.title))}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"bg-gradient-to-r from-primary/5 to-accent/5 rounded-xl p-6 border border-primary/10",children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-3",children:"Response Times"}),(0,d.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Online Form:"}),(0,d.jsx)("span",{className:"font-semibold text-gray-900",children:"Within 24 hours"})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Phone Calls:"}),(0,d.jsx)("span",{className:"font-semibold text-gray-900",children:"Immediate"})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Email:"}),(0,d.jsx)("span",{className:"font-semibold text-gray-900",children:"Within 24 hours"})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Emergency:"}),(0,d.jsx)("span",{className:"font-semibold text-red-600",children:"24/7 Available"})]})]})]})]}),(0,d.jsx)(e.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:(0,d.jsx)(k.A,{title:"Send us a Message",showCard:!0})})]})]})}},71134:(a,b,c)=>{"use strict";c.d(b,{X:()=>g,w:()=>f});var d=c(60687),e=c(4780);function f({children:a,className:b,id:c,background:f="white"}){return(0,d.jsx)("section",{id:c,className:(0,e.cn)("py-16 sm:py-20",{white:"bg-white",gray:"bg-gray-50",primary:"bg-primary text-primary-foreground"}[f],b),children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a})})}function g({title:a,subtitle:b,description:c,centered:f=!0,className:g}){return(0,d.jsxs)("div",{className:(0,e.cn)("mb-12",f&&"text-center",g),children:[b&&(0,d.jsx)("p",{className:"text-primary font-semibold text-sm uppercase tracking-wide mb-2",children:b}),(0,d.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:a}),c&&(0,d.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:c})]})}},76199:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\dental\\\\dental-clinic\\\\src\\\\components\\\\sections\\\\ContactHero.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\components\\sections\\ContactHero.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,472,753],()=>b(b.s=27868));module.exports=c})();