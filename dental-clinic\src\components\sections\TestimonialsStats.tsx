'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { Star, TrendingUp, Award, Users, ArrowRight } from 'lucide-react';
import { Section } from '@/components/ui/section';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

const satisfactionMetrics = [
  {
    icon: Star,
    title: 'Service Quality',
    percentage: 98,
    description: 'Patients rate our service quality as excellent'
  },
  {
    icon: Users,
    title: 'Staff Friendliness',
    percentage: 97,
    description: 'Patients appreciate our caring and friendly staff'
  },
  {
    icon: TrendingUp,
    title: 'Treatment Effectiveness',
    percentage: 96,
    description: 'Successful treatment outcomes reported'
  },
  {
    icon: Award,
    title: 'Overall Experience',
    percentage: 95,
    description: 'Would recommend us to friends and family'
  }
];

const reviewHighlights = [
  {
    category: 'Most Appreciated',
    items: ['Professional staff', 'Clean facilities', 'Pain-free treatments', 'Clear explanations']
  },
  {
    category: 'Top Services',
    items: ['Teeth cleaning', 'Dental implants', 'Orthodontics', 'Root canal treatment']
  },
  {
    category: 'Patient Demographics',
    items: ['Families with children', 'Working professionals', 'Senior citizens', 'Students']
  }
];

export default function TestimonialsStats() {
  return (
    <Section background="gray">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
        {/* Satisfaction Metrics */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Patient Satisfaction Metrics
            </h2>
            <p className="text-gray-600 leading-relaxed">
              Our commitment to excellence is reflected in these satisfaction scores 
              based on patient feedback and reviews.
            </p>
          </div>

          <div className="space-y-6">
            {satisfactionMetrics.map((metric, index) => (
              <motion.div
                key={metric.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-xl p-6 shadow-sm border border-gray-100"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <metric.icon className="h-5 w-5 text-primary" />
                    </div>
                    <h3 className="font-semibold text-gray-900">{metric.title}</h3>
                  </div>
                  <span className="text-2xl font-bold text-primary">{metric.percentage}%</span>
                </div>
                
                {/* Progress Bar */}
                <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                  <motion.div
                    initial={{ width: 0 }}
                    whileInView={{ width: `${metric.percentage}%` }}
                    transition={{ duration: 1, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-primary h-2 rounded-full"
                  />
                </div>
                
                <p className="text-sm text-gray-600">{metric.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Review Highlights */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Review Highlights
            </h2>
            <p className="text-gray-600 leading-relaxed">
              Common themes and highlights from our patient reviews and testimonials.
            </p>
          </div>

          <div className="space-y-6">
            {reviewHighlights.map((highlight, index) => (
              <motion.div
                key={highlight.category}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="hover:shadow-md transition-shadow duration-300">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      {highlight.category}
                    </h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      {highlight.items.map((item, itemIndex) => (
                        <div
                          key={itemIndex}
                          className="flex items-center space-x-2 text-sm text-gray-600"
                        >
                          <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0" />
                          <span>{item}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* CTA Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="mt-16 text-center"
      >
        <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Ready to Experience Exceptional Dental Care?
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Join thousands of satisfied patients who have trusted us with their dental care.
            Schedule your appointment today and discover why we&apos;re Biratnagar&apos;s preferred dental clinic.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/contact">Book Your Appointment</Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link href="/services">
                Explore Our Services
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </motion.div>
    </Section>
  );
}
