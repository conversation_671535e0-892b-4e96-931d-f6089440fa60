(()=>{var a={};a.id=759,a.ids=[759],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23282:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\dental\\\\dental-clinic\\\\src\\\\components\\\\sections\\\\TestimonialsStats.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\components\\sections\\TestimonialsStats.tsx","default")},24071:(a,b,c)=>{Promise.resolve().then(c.bind(c,49049)),Promise.resolve().then(c.bind(c,51925)),Promise.resolve().then(c.bind(c,99464))},25611:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(60687),e=c(51743),f=c(64398),g=c(98916),h=c(44493),i=c(96834);function j({testimonial:a,index:b=0,showService:c=!0}){let j;return(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*b},children:(0,d.jsx)(h.Zp,{className:"h-full hover:shadow-lg transition-shadow duration-300",children:(0,d.jsxs)(h.Wu,{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,d.jsx)(g.A,{className:"h-8 w-8 text-primary opacity-50"}),(0,d.jsx)("div",{className:"flex space-x-1",children:(j=a.rating,Array.from({length:5},(a,b)=>(0,d.jsx)(f.A,{className:`h-4 w-4 ${b<j?"text-yellow-400 fill-current":"text-gray-300"}`},b)))})]}),(0,d.jsxs)("blockquote",{className:"text-gray-700 mb-6 leading-relaxed",children:['"',a.review,'"']}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-full overflow-hidden",children:(0,d.jsx)("img",{src:a.image||"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",alt:a.name,className:"w-full h-full object-cover",onError:a=>{a.target.src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"}})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-semibold text-gray-900",children:a.name}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:a.city})]})]}),c&&(0,d.jsx)(i.E,{variant:"outline",className:"text-xs",children:a.service})]}),(0,d.jsx)("div",{className:"mt-4 text-xs text-gray-500",children:new Date(a.date).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]})})})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41312:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},44493:(a,b,c)=>{"use strict";c.d(b,{Wu:()=>i,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},46775:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\dental\\\\dental-clinic\\\\src\\\\components\\\\sections\\\\TestimonialsGrid.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\components\\sections\\TestimonialsGrid.tsx","default")},49049:(a,b,c)=>{"use strict";c.d(b,{default:()=>l});var d=c(60687),e=c(43210),f=c(51743);let g=(0,c(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]);var h=c(71134),i=c(29523),j=c(25611),k=c(83446);function l(){let[a,b]=(0,e.useState)("all"),c=["all",...Array.from(new Set(k.rR.map(a=>a.service)))],l="all"===a?k.rR:k.rR.filter(b=>b.service===a);return(0,d.jsxs)(h.w,{background:"white",children:[(0,d.jsx)(h.X,{subtitle:"Patient Reviews",title:"Real Stories from Real Patients",description:"Read authentic testimonials from our patients who have experienced our dental care services. Their stories speak to our commitment to excellence."}),(0,d.jsxs)("div",{className:"flex flex-wrap justify-center gap-3 mb-12",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600 mr-4",children:[(0,d.jsx)(g,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Filter by service:"})]}),c.map(c=>(0,d.jsx)(i.$,{variant:a===c?"default":"outline",size:"sm",onClick:()=>b(c),children:"all"===c?"All Services":c},c))]}),(0,d.jsx)(f.P.div,{layout:!0,className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:l.map((b,c)=>(0,d.jsx)(j.A,{testimonial:b,index:c,showService:"all"===a},b.id))}),0===l.length&&(0,d.jsxs)(f.P.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center py-12",children:[(0,d.jsx)("p",{className:"text-gray-600 text-lg",children:"No testimonials found for the selected service."}),(0,d.jsx)(i.$,{variant:"outline",onClick:()=>b("all"),className:"mt-4",children:"Show All Testimonials"})]}),(0,d.jsx)(f.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mt-16 text-center",children:(0,d.jsxs)("div",{className:"bg-gradient-to-r from-primary/5 to-accent/5 rounded-2xl p-8 border border-primary/10",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Share Your Experience"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Have you been treated at our clinic? We'd love to hear about your experience. Your feedback helps us improve and helps other patients make informed decisions."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(i.$,{size:"lg",children:"Leave a Review"}),(0,d.jsx)(i.$,{variant:"outline",size:"lg",children:"Contact Us"})]})]})})]})}},51925:(a,b,c)=>{"use strict";c.d(b,{default:()=>l});var d=c(60687),e=c(51743),f=c(64398),g=c(41312),h=c(67760),i=c(98916),j=c(71134);let k=[{icon:f.A,title:"Average Rating",value:"4.9/5",description:"Based on 500+ reviews"},{icon:g.A,title:"Happy Patients",value:"5000+",description:"Served since 2015"},{icon:h.A,title:"Satisfaction Rate",value:"98%",description:"Would recommend us"},{icon:i.A,title:"Testimonials",value:"200+",description:"Written reviews"}];function l(){return(0,d.jsxs)(j.w,{background:"gray",className:"pt-32 pb-20",children:[(0,d.jsx)("div",{className:"text-center max-w-4xl mx-auto mb-16",children:(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,d.jsx)("p",{className:"text-primary font-semibold text-sm uppercase tracking-wide mb-4",children:"Patient Testimonials"}),(0,d.jsxs)("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6",children:["What Our"," ",(0,d.jsx)("span",{className:"text-primary",children:"Patients Say"})]}),(0,d.jsx)("p",{className:"text-xl text-gray-600 leading-relaxed mb-8",children:"Don't just take our word for it. Read genuine reviews from our satisfied patients who have experienced our exceptional dental care firsthand."}),(0,d.jsxs)("div",{className:"inline-flex items-center space-x-2 bg-white px-8 py-4 rounded-full shadow-sm border border-gray-100",children:[(0,d.jsx)("div",{className:"flex space-x-1",children:[1,2,3,4,5].map(a=>(0,d.jsx)(f.A,{className:"h-6 w-6 text-yellow-400 fill-current"},a))}),(0,d.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"4.9"}),(0,d.jsx)("span",{className:"text-gray-600",children:"out of 5 stars"})]})]})}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:k.map((a,b)=>(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center hover:shadow-md transition-shadow duration-300",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(a.icon,{className:"h-8 w-8 text-primary"})}),(0,d.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:a.value}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:a.title}),(0,d.jsx)("p",{className:"text-gray-600 text-sm",children:a.description})]},a.title))})]})}},54748:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["testimonials",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,57339)),"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\app\\testimonials\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,92302)),"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\app\\testimonials\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/testimonials/page",pathname:"/testimonials",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/testimonials/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},57339:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i,metadata:()=>h});var d=c(37413),e=c(74599),f=c(46775),g=c(23282);let h={title:"Patient Testimonials | Dental Care Clinic Biratnagar - Reviews & Feedback",description:"Read genuine patient testimonials and reviews about our dental services in Biratnagar. See why patients trust us for their oral health care needs.",keywords:"dental clinic reviews Biratnagar, patient testimonials, dental care feedback, dentist reviews Nepal"};function i(){return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(e.default,{}),(0,d.jsx)(f.default,{}),(0,d.jsx)(g.default,{})]})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64239:(a,b,c)=>{Promise.resolve().then(c.bind(c,46775)),Promise.resolve().then(c.bind(c,74599)),Promise.resolve().then(c.bind(c,23282))},64398:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},67760:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},70334:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},71134:(a,b,c)=>{"use strict";c.d(b,{X:()=>g,w:()=>f});var d=c(60687),e=c(4780);function f({children:a,className:b,id:c,background:f="white"}){return(0,d.jsx)("section",{id:c,className:(0,e.cn)("py-16 sm:py-20",{white:"bg-white",gray:"bg-gray-50",primary:"bg-primary text-primary-foreground"}[f],b),children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a})})}function g({title:a,subtitle:b,description:c,centered:f=!0,className:g}){return(0,d.jsxs)("div",{className:(0,e.cn)("mb-12",f&&"text-center",g),children:[b&&(0,d.jsx)("p",{className:"text-primary font-semibold text-sm uppercase tracking-wide mb-2",children:b}),(0,d.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:a}),c&&(0,d.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:c})]})}},74599:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\dental\\\\dental-clinic\\\\src\\\\components\\\\sections\\\\TestimonialsHero.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\components\\sections\\TestimonialsHero.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86561:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},96834:(a,b,c)=>{"use strict";c.d(b,{E:()=>i});var d=c(60687);c(43210);var e=c(81391),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:a,variant:b,asChild:c=!1,...f}){let i=c?e.DX:"span";return(0,d.jsx)(i,{"data-slot":"badge",className:(0,g.cn)(h({variant:b}),a),...f})}},98916:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]])},99464:(a,b,c)=>{"use strict";c.d(b,{default:()=>r});var d=c(60687),e=c(51743),f=c(85814),g=c.n(f),h=c(64398),i=c(41312);let j=(0,c(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var k=c(86561),l=c(70334),m=c(71134),n=c(29523),o=c(44493);let p=[{icon:h.A,title:"Service Quality",percentage:98,description:"Patients rate our service quality as excellent"},{icon:i.A,title:"Staff Friendliness",percentage:97,description:"Patients appreciate our caring and friendly staff"},{icon:j,title:"Treatment Effectiveness",percentage:96,description:"Successful treatment outcomes reported"},{icon:k.A,title:"Overall Experience",percentage:95,description:"Would recommend us to friends and family"}],q=[{category:"Most Appreciated",items:["Professional staff","Clean facilities","Pain-free treatments","Clear explanations"]},{category:"Top Services",items:["Teeth cleaning","Dental implants","Orthodontics","Root canal treatment"]},{category:"Patient Demographics",items:["Families with children","Working professionals","Senior citizens","Students"]}];function r(){return(0,d.jsxs)(m.w,{background:"gray",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Patient Satisfaction Metrics"}),(0,d.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Our commitment to excellence is reflected in these satisfaction scores based on patient feedback and reviews."})]}),(0,d.jsx)("div",{className:"space-y-6",children:p.map((a,b)=>(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},viewport:{once:!0},className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center",children:(0,d.jsx)(a.icon,{className:"h-5 w-5 text-primary"})}),(0,d.jsx)("h3",{className:"font-semibold text-gray-900",children:a.title})]}),(0,d.jsxs)("span",{className:"text-2xl font-bold text-primary",children:[a.percentage,"%"]})]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mb-2",children:(0,d.jsx)(e.P.div,{initial:{width:0},whileInView:{width:`${a.percentage}%`},transition:{duration:1,delay:.1*b},viewport:{once:!0},className:"bg-primary h-2 rounded-full"})}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:a.description})]},a.title))})]}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Review Highlights"}),(0,d.jsx)("p",{className:"text-gray-600 leading-relaxed",children:"Common themes and highlights from our patient reviews and testimonials."})]}),(0,d.jsx)("div",{className:"space-y-6",children:q.map((a,b)=>(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},viewport:{once:!0},children:(0,d.jsx)(o.Zp,{className:"hover:shadow-md transition-shadow duration-300",children:(0,d.jsxs)(o.Wu,{className:"p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:a.category}),(0,d.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:a.items.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full flex-shrink-0"}),(0,d.jsx)("span",{children:a})]},b))})]})})},a.category))})]})]}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mt-16 text-center",children:(0,d.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Ready to Experience Exceptional Dental Care?"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Join thousands of satisfied patients who have trusted us with their dental care. Schedule your appointment today and discover why we're Biratnagar's preferred dental clinic."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(n.$,{asChild:!0,size:"lg",children:(0,d.jsx)(g(),{href:"/contact",children:"Book Your Appointment"})}),(0,d.jsx)(n.$,{asChild:!0,variant:"outline",size:"lg",children:(0,d.jsxs)(g(),{href:"/services",children:["Explore Our Services",(0,d.jsx)(l.A,{className:"ml-2 h-4 w-4"})]})})]})]})})]})}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,472,753],()=>b(b.s=54748));module.exports=c})();