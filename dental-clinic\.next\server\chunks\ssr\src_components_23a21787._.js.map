{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/dental/dental-clinic/src/components/sections/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { Button } from '@/components/ui/button';\nimport { Phone, MapPin, Clock } from 'lucide-react';\nimport { clinicInfo, contactInfo } from '@/data/mockData';\n\nexport default function HeroSection() {\n  return (\n    <section className=\"relative bg-gradient-to-br from-blue-50 via-white to-teal-50 py-20 sm:py-32 overflow-hidden\">\n      {/* Background decoration */}\n      <div className=\"absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] -z-10\" />\n      \n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"space-y-8\"\n          >\n            <div className=\"space-y-4\">\n              <motion.h1\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.2 }}\n                className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight\"\n              >\n                Your Smile,{' '}\n                <span className=\"text-primary\">Our Priority</span>\n              </motion.h1>\n              \n              <motion.p\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.4 }}\n                className=\"text-xl text-gray-600 leading-relaxed max-w-2xl\"\n              >\n                Experience exceptional dental care in the heart of Biratnagar. Our expert team provides comprehensive dental services with the latest technology and a gentle touch.\n              </motion.p>\n            </div>\n\n            {/* Quick Info */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n              className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 py-6\"\n            >\n              <div className=\"flex items-center space-x-3 text-sm\">\n                <div className=\"w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center\">\n                  <Phone className=\"h-5 w-5 text-primary\" />\n                </div>\n                <div>\n                  <p className=\"font-semibold text-gray-900\">Call Us</p>\n                  <p className=\"text-gray-600\">{contactInfo.phone}</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-3 text-sm\">\n                <div className=\"w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center\">\n                  <MapPin className=\"h-5 w-5 text-primary\" />\n                </div>\n                <div>\n                  <p className=\"font-semibold text-gray-900\">Location</p>\n                  <p className=\"text-gray-600\">Biratnagar, Nepal</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-3 text-sm\">\n                <div className=\"w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center\">\n                  <Clock className=\"h-5 w-5 text-primary\" />\n                </div>\n                <div>\n                  <p className=\"font-semibold text-gray-900\">Open Today</p>\n                  <p className=\"text-gray-600\">10:00 AM - 6:00 PM</p>\n                </div>\n              </div>\n            </motion.div>\n\n            {/* CTA Buttons */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.8 }}\n              className=\"flex flex-col sm:flex-row gap-4\"\n            >\n              <Button asChild size=\"lg\" className=\"text-lg px-8 py-6\">\n                <Link href=\"/contact\">Book Appointment</Link>\n              </Button>\n              <Button asChild variant=\"outline\" size=\"lg\" className=\"text-lg px-8 py-6\">\n                <Link href=\"/services\">Our Services</Link>\n              </Button>\n            </motion.div>\n          </motion.div>\n\n          {/* Hero Image */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"relative\"\n          >\n            <div className=\"relative w-full h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl\">\n              <img\n                src=\"https://images.unsplash.com/photo-1629909613654-28e377c37b09?w=600&h=500&fit=crop&crop=center\"\n                alt=\"Modern dental clinic interior\"\n                className=\"w-full h-full object-cover\"\n              />\n              \n              {/* Floating card */}\n              <motion.div\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.8, delay: 1.2 }}\n                className=\"absolute bottom-6 left-6 right-6 bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg\"\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <p className=\"font-semibold text-gray-900\">Expert Care Since 2015</p>\n                    <p className=\"text-sm text-gray-600\">Trusted by 5000+ patients</p>\n                  </div>\n                  <div className=\"flex -space-x-2\">\n                    {[1, 2, 3].map((i) => (\n                      <div\n                        key={i}\n                        className=\"w-8 h-8 bg-gray-300 rounded-full border-2 border-white\"\n                        style={{\n                          backgroundImage: `url(https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face)`,\n                          backgroundSize: 'cover'\n                        }}\n                      />\n                    ))}\n                    <div className=\"w-8 h-8 bg-primary rounded-full border-2 border-white flex items-center justify-center\">\n                      <span className=\"text-xs text-white font-semibold\">+</span>\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,WAAU;;gDACX;gDACa;8DACZ,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAGjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,WAAU;sDACX;;;;;;;;;;;;8CAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA8B;;;;;;sEAC3C,8OAAC;4DAAE,WAAU;sEAAiB,uHAAA,CAAA,cAAW,CAAC,KAAK;;;;;;;;;;;;;;;;;;sDAInD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA8B;;;;;;sEAC3C,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAIjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA8B;;;;;;sEAC3C,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;8CAMnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,MAAK;4CAAK,WAAU;sDAClC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAW;;;;;;;;;;;sDAExB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDACpD,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAY;;;;;;;;;;;;;;;;;;;;;;;sCAM7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,KAAI;wCACJ,KAAI;wCACJ,WAAU;;;;;;kDAIZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA8B;;;;;;sEAC3C,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAEvC,8OAAC;oDAAI,WAAU;;wDACZ;4DAAC;4DAAG;4DAAG;yDAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;gEAEC,WAAU;gEACV,OAAO;oEACL,iBAAiB,CAAC,gGAAgG,CAAC;oEACnH,gBAAgB;gEAClB;+DALK;;;;;sEAQT,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWzE", "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/dental/dental-clinic/src/components/ui/section.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils';\n\ninterface SectionProps {\n  children: React.ReactNode;\n  className?: string;\n  id?: string;\n  background?: 'white' | 'gray' | 'primary';\n}\n\nexport function Section({ \n  children, \n  className, \n  id, \n  background = 'white' \n}: SectionProps) {\n  const backgroundClasses = {\n    white: 'bg-white',\n    gray: 'bg-gray-50',\n    primary: 'bg-primary text-primary-foreground'\n  };\n\n  return (\n    <section \n      id={id}\n      className={cn(\n        'py-16 sm:py-20',\n        backgroundClasses[background],\n        className\n      )}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {children}\n      </div>\n    </section>\n  );\n}\n\ninterface SectionHeaderProps {\n  title: string;\n  subtitle?: string;\n  description?: string;\n  centered?: boolean;\n  className?: string;\n}\n\nexport function SectionHeader({ \n  title, \n  subtitle, \n  description, \n  centered = true,\n  className \n}: SectionHeaderProps) {\n  return (\n    <div className={cn(\n      'mb-12',\n      centered && 'text-center',\n      className\n    )}>\n      {subtitle && (\n        <p className=\"text-primary font-semibold text-sm uppercase tracking-wide mb-2\">\n          {subtitle}\n        </p>\n      )}\n      <h2 className=\"text-3xl sm:text-4xl font-bold text-gray-900 mb-4\">\n        {title}\n      </h2>\n      {description && (\n        <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n          {description}\n        </p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AASO,SAAS,QAAQ,EACtB,QAAQ,EACR,SAAS,EACT,EAAE,EACF,aAAa,OAAO,EACP;IACb,MAAM,oBAAoB;QACxB,OAAO;QACP,MAAM;QACN,SAAS;IACX;IAEA,qBACE,8OAAC;QACC,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kBACA,iBAAiB,CAAC,WAAW,EAC7B;kBAGF,cAAA,8OAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;AAUO,SAAS,cAAc,EAC5B,KAAK,EACL,QAAQ,EACR,WAAW,EACX,WAAW,IAAI,EACf,SAAS,EACU;IACnB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,SACA,YAAY,eACZ;;YAEC,0BACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;0BAGL,8OAAC;gBAAG,WAAU;0BACX;;;;;;YAEF,6BACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 557, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/dental/dental-clinic/src/components/sections/IntroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Heart, Shield, Users, Award } from 'lucide-react';\nimport { Section, SectionHeader } from '@/components/ui/section';\nimport { clinicInfo } from '@/data/mockData';\n\nconst features = [\n  {\n    icon: Heart,\n    title: 'Compassionate Care',\n    description: 'We treat every patient with kindness, understanding, and respect, ensuring a comfortable experience.'\n  },\n  {\n    icon: Shield,\n    title: 'Advanced Technology',\n    description: 'State-of-the-art equipment and modern techniques for precise, effective dental treatments.'\n  },\n  {\n    icon: Users,\n    title: 'Expert Team',\n    description: 'Highly qualified dentists and staff with years of experience in comprehensive dental care.'\n  },\n  {\n    icon: Award,\n    title: 'Quality Assurance',\n    description: 'Committed to maintaining the highest standards of dental care and patient satisfaction.'\n  }\n];\n\nexport default function IntroSection() {\n  return (\n    <Section background=\"white\">\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\n        {/* Content */}\n        <motion.div\n          initial={{ opacity: 0, x: -50 }}\n          whileInView={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"space-y-8\"\n        >\n          <div className=\"space-y-4\">\n            <p className=\"text-primary font-semibold text-sm uppercase tracking-wide\">\n              About Our Clinic\n            </p>\n            <h2 className=\"text-3xl sm:text-4xl font-bold text-gray-900\">\n              Dedicated to Your{' '}\n              <span className=\"text-primary\">Oral Health</span>\n            </h2>\n            <p className=\"text-lg text-gray-600 leading-relaxed\">\n              {clinicInfo.mission}\n            </p>\n            <p className=\"text-gray-600 leading-relaxed\">\n              {clinicInfo.story}\n            </p>\n          </div>\n\n          {/* Stats */}\n          <div className=\"grid grid-cols-2 gap-6\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              viewport={{ once: true }}\n              className=\"text-center\"\n            >\n              <div className=\"text-3xl font-bold text-primary mb-2\">5000+</div>\n              <div className=\"text-sm text-gray-600\">Happy Patients</div>\n            </motion.div>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.3 }}\n              viewport={{ once: true }}\n              className=\"text-center\"\n            >\n              <div className=\"text-3xl font-bold text-primary mb-2\">9+</div>\n              <div className=\"text-sm text-gray-600\">Years Experience</div>\n            </motion.div>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.4 }}\n              viewport={{ once: true }}\n              className=\"text-center\"\n            >\n              <div className=\"text-3xl font-bold text-primary mb-2\">15+</div>\n              <div className=\"text-sm text-gray-600\">Services Offered</div>\n            </motion.div>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.5 }}\n              viewport={{ once: true }}\n              className=\"text-center\"\n            >\n              <div className=\"text-3xl font-bold text-primary mb-2\">98%</div>\n              <div className=\"text-sm text-gray-600\">Satisfaction Rate</div>\n            </motion.div>\n          </div>\n        </motion.div>\n\n        {/* Features Grid */}\n        <motion.div\n          initial={{ opacity: 0, x: 50 }}\n          whileInView={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"grid grid-cols-1 sm:grid-cols-2 gap-6\"\n        >\n          {features.map((feature, index) => (\n            <motion.div\n              key={feature.title}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300\"\n            >\n              <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4\">\n                <feature.icon className=\"h-6 w-6 text-primary\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                {feature.title}\n              </h3>\n              <p className=\"text-gray-600 text-sm leading-relaxed\">\n                {feature.description}\n              </p>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAOA,MAAM,WAAW;IACf;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,YAAW;kBAClB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA6D;;;;;;8CAG1E,8OAAC;oCAAG,WAAU;;wCAA+C;wCACzC;sDAClB,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEjC,8OAAC;oCAAE,WAAU;8CACV,uHAAA,CAAA,aAAU,CAAC,OAAO;;;;;;8CAErB,8OAAC;oCAAE,WAAU;8CACV,uHAAA,CAAA,aAAU,CAAC,KAAK;;;;;;;;;;;;sCAKrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDAAuC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDAAuC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDAAuC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDAAuC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;8BAM7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAET,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,QAAQ,IAAI;wCAAC,WAAU;;;;;;;;;;;8CAE1B,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;2BAdjB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;AAsBhC", "debugId": null}}, {"offset": {"line": 937, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/dental/dental-clinic/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1032, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/dental/dental-clinic/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/dental/dental-clinic/src/components/ui/service-card.tsx"], "sourcesContent": ["import { motion } from 'framer-motion';\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Service } from '@/types';\n\ninterface ServiceCardProps {\n  service: Service;\n  index?: number;\n  showPrice?: boolean;\n  showFullDescription?: boolean;\n}\n\nexport default function ServiceCard({ \n  service, \n  index = 0, \n  showPrice = false,\n  showFullDescription = false \n}: ServiceCardProps) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5, delay: index * 0.1 }}\n    >\n      <Card className=\"h-full hover:shadow-lg transition-shadow duration-300\">\n        <CardHeader>\n          <div className=\"w-full h-48 bg-gray-200 rounded-lg mb-4 overflow-hidden\">\n            <img\n              src={service.image}\n              alt={service.name}\n              className=\"w-full h-full object-cover\"\n              onError={(e) => {\n                const target = e.target as HTMLImageElement;\n                target.src = `https://images.unsplash.com/photo-1606811841689-23dfddce3e95?w=400&h=300&fit=crop&crop=center`;\n              }}\n            />\n          </div>\n          <CardTitle className=\"text-xl font-bold text-gray-900\">\n            {service.name}\n          </CardTitle>\n          {showPrice && service.price && (\n            <Badge variant=\"secondary\" className=\"w-fit\">\n              {service.price}\n            </Badge>\n          )}\n        </CardHeader>\n        <CardContent>\n          <p className=\"text-gray-600 mb-4\">\n            {service.description}\n          </p>\n          \n          {showFullDescription && (\n            <>\n              <div className=\"mb-4\">\n                <h4 className=\"font-semibold text-gray-900 mb-2\">Benefits:</h4>\n                <ul className=\"list-disc list-inside space-y-1 text-sm text-gray-600\">\n                  {service.benefits.map((benefit, idx) => (\n                    <li key={idx}>{benefit}</li>\n                  ))}\n                </ul>\n              </div>\n              \n              <div>\n                <h4 className=\"font-semibold text-gray-900 mb-2\">Procedure:</h4>\n                <p className=\"text-sm text-gray-600\">{service.procedure}</p>\n              </div>\n            </>\n          )}\n        </CardContent>\n      </Card>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAUe,SAAS,YAAY,EAClC,OAAO,EACP,QAAQ,CAAC,EACT,YAAY,KAAK,EACjB,sBAAsB,KAAK,EACV;IACjB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAI;kBAEhD,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;;sCACT,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,KAAK,QAAQ,KAAK;gCAClB,KAAK,QAAQ,IAAI;gCACjB,WAAU;gCACV,SAAS,CAAC;oCACR,MAAM,SAAS,EAAE,MAAM;oCACvB,OAAO,GAAG,GAAG,CAAC,6FAA6F,CAAC;gCAC9G;;;;;;;;;;;sCAGJ,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAClB,QAAQ,IAAI;;;;;;wBAEd,aAAa,QAAQ,KAAK,kBACzB,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAY,WAAU;sCAClC,QAAQ,KAAK;;;;;;;;;;;;8BAIpB,8OAAC,gIAAA,CAAA,cAAW;;sCACV,8OAAC;4BAAE,WAAU;sCACV,QAAQ,WAAW;;;;;;wBAGrB,qCACC;;8CACE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAG,WAAU;sDACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC9B,8OAAC;8DAAc;mDAAN;;;;;;;;;;;;;;;;8CAKf,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAyB,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvE", "debugId": null}}, {"offset": {"line": 1240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/dental/dental-clinic/src/components/sections/ServicesOverview.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { ArrowRight } from 'lucide-react';\nimport { Section, SectionHeader } from '@/components/ui/section';\nimport { Button } from '@/components/ui/button';\nimport ServiceCard from '@/components/ui/service-card';\nimport { services } from '@/data/mockData';\n\nexport default function ServicesOverview() {\n  // Show only the first 6 services on homepage\n  const featuredServices = services.slice(0, 6);\n\n  return (\n    <Section background=\"gray\">\n      <SectionHeader\n        subtitle=\"Our Services\"\n        title=\"Comprehensive Dental Care\"\n        description=\"We offer a full range of dental services to keep your smile healthy and beautiful. From routine cleanings to advanced procedures, our expert team is here to help.\"\n      />\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\">\n        {featuredServices.map((service, index) => (\n          <ServiceCard\n            key={service.id}\n            service={service}\n            index={index}\n            showPrice={false}\n            showFullDescription={false}\n          />\n        ))}\n      </div>\n\n      {/* CTA Section */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8 }}\n        viewport={{ once: true }}\n        className=\"text-center\"\n      >\n        <div className=\"bg-white rounded-2xl p-8 shadow-sm border border-gray-100\">\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n            Need a Specific Treatment?\n          </h3>\n          <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n            Can't find what you're looking for? We offer many more specialized dental services. \n            Contact us to discuss your specific needs or browse our complete service list.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button asChild size=\"lg\">\n              <Link href=\"/services\">\n                View All Services\n                <ArrowRight className=\"ml-2 h-4 w-4\" />\n              </Link>\n            </Button>\n            <Button asChild variant=\"outline\" size=\"lg\">\n              <Link href=\"/contact\">Schedule Consultation</Link>\n            </Button>\n          </div>\n        </div>\n      </motion.div>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,6CAA6C;IAC7C,MAAM,mBAAmB,uHAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,GAAG;IAE3C,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,YAAW;;0BAClB,8OAAC,mIAAA,CAAA,gBAAa;gBACZ,UAAS;gBACT,OAAM;gBACN,aAAY;;;;;;0BAGd,8OAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,2IAAA,CAAA,UAAW;wBAEV,SAAS;wBACT,OAAO;wBACP,WAAW;wBACX,qBAAqB;uBAJhB,QAAQ,EAAE;;;;;;;;;;0BAUrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,UAAU;oBAAE,MAAM;gBAAK;gBACvB,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAuC;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,MAAK;8CACnB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;4CAAY;0DAErB,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG1B,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,SAAQ;oCAAU,MAAK;8CACrC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 1400, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/dental/dental-clinic/src/components/ui/testimonial-card.tsx"], "sourcesContent": ["import { motion } from 'framer-motion';\nimport { Star, Quote } from 'lucide-react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Testimonial } from '@/types';\n\ninterface TestimonialCardProps {\n  testimonial: Testimonial;\n  index?: number;\n  showService?: boolean;\n}\n\nexport default function TestimonialCard({ \n  testimonial, \n  index = 0,\n  showService = true \n}: TestimonialCardProps) {\n  const renderStars = (rating: number) => {\n    return Array.from({ length: 5 }, (_, i) => (\n      <Star\n        key={i}\n        className={`h-4 w-4 ${\n          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'\n        }`}\n      />\n    ));\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5, delay: index * 0.1 }}\n    >\n      <Card className=\"h-full hover:shadow-lg transition-shadow duration-300\">\n        <CardContent className=\"p-6\">\n          <div className=\"flex items-start justify-between mb-4\">\n            <Quote className=\"h-8 w-8 text-primary opacity-50\" />\n            <div className=\"flex space-x-1\">\n              {renderStars(testimonial.rating)}\n            </div>\n          </div>\n          \n          <blockquote className=\"text-gray-700 mb-6 leading-relaxed\">\n            \"{testimonial.review}\"\n          </blockquote>\n          \n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-12 h-12 bg-gray-200 rounded-full overflow-hidden\">\n                <img\n                  src={testimonial.image || `https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face`}\n                  alt={testimonial.name}\n                  className=\"w-full h-full object-cover\"\n                  onError={(e) => {\n                    const target = e.target as HTMLImageElement;\n                    target.src = `https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face`;\n                  }}\n                />\n              </div>\n              <div>\n                <p className=\"font-semibold text-gray-900\">{testimonial.name}</p>\n                <p className=\"text-sm text-gray-600\">{testimonial.city}</p>\n              </div>\n            </div>\n            \n            {showService && (\n              <Badge variant=\"outline\" className=\"text-xs\">\n                {testimonial.service}\n              </Badge>\n            )}\n          </div>\n          \n          <div className=\"mt-4 text-xs text-gray-500\">\n            {new Date(testimonial.date).toLocaleDateString('en-US', {\n              year: 'numeric',\n              month: 'long',\n              day: 'numeric'\n            })}\n          </div>\n        </CardContent>\n      </Card>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;;;;;;AASe,SAAS,gBAAgB,EACtC,WAAW,EACX,QAAQ,CAAC,EACT,cAAc,IAAI,EACG;IACrB,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,kBACnC,8OAAC,kMAAA,CAAA,OAAI;gBAEH,WAAW,CAAC,QAAQ,EAClB,IAAI,SAAS,iCAAiC,iBAC9C;eAHG;;;;;IAMX;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAI;kBAEhD,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAI,WAAU;0CACZ,YAAY,YAAY,MAAM;;;;;;;;;;;;kCAInC,8OAAC;wBAAW,WAAU;;4BAAqC;4BACvD,YAAY,MAAM;4BAAC;;;;;;;kCAGvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,KAAK,YAAY,KAAK,IAAI,CAAC,2FAA2F,CAAC;4CACvH,KAAK,YAAY,IAAI;4CACrB,WAAU;4CACV,SAAS,CAAC;gDACR,MAAM,SAAS,EAAE,MAAM;gDACvB,OAAO,GAAG,GAAG,CAAC,2FAA2F,CAAC;4CAC5G;;;;;;;;;;;kDAGJ,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA+B,YAAY,IAAI;;;;;;0DAC5D,8OAAC;gDAAE,WAAU;0DAAyB,YAAY,IAAI;;;;;;;;;;;;;;;;;;4BAIzD,6BACC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAChC,YAAY,OAAO;;;;;;;;;;;;kCAK1B,8OAAC;wBAAI,WAAU;kCACZ,IAAI,KAAK,YAAY,IAAI,EAAE,kBAAkB,CAAC,SAAS;4BACtD,MAAM;4BACN,OAAO;4BACP,KAAK;wBACP;;;;;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 1584, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/dental/dental-clinic/src/components/sections/TestimonialsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { ArrowR<PERSON>, Star } from 'lucide-react';\nimport { Section, SectionHeader } from '@/components/ui/section';\nimport { Button } from '@/components/ui/button';\nimport TestimonialCard from '@/components/ui/testimonial-card';\nimport { testimonials } from '@/data/mockData';\n\nexport default function TestimonialsSection() {\n  // Show only the first 3 testimonials on homepage\n  const featuredTestimonials = testimonials.slice(0, 3);\n\n  return (\n    <Section background=\"white\">\n      <SectionHeader\n        subtitle=\"Patient Reviews\"\n        title=\"What Our Patients Say\"\n        description=\"Don't just take our word for it. Here's what our patients have to say about their experience at our clinic.\"\n      />\n\n      {/* Rating Summary */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8 }}\n        viewport={{ once: true }}\n        className=\"text-center mb-12\"\n      >\n        <div className=\"inline-flex items-center space-x-2 bg-yellow-50 px-6 py-3 rounded-full\">\n          <div className=\"flex space-x-1\">\n            {[1, 2, 3, 4, 5].map((star) => (\n              <Star key={star} className=\"h-5 w-5 text-yellow-400 fill-current\" />\n            ))}\n          </div>\n          <span className=\"text-lg font-semibold text-gray-900\">4.9/5</span>\n          <span className=\"text-gray-600\">from 500+ reviews</span>\n        </div>\n      </motion.div>\n\n      {/* Testimonials Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\">\n        {featuredTestimonials.map((testimonial, index) => (\n          <TestimonialCard\n            key={testimonial.id}\n            testimonial={testimonial}\n            index={index}\n            showService={true}\n          />\n        ))}\n      </div>\n\n      {/* CTA Section */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        whileInView={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8 }}\n        viewport={{ once: true }}\n        className=\"text-center\"\n      >\n        <div className=\"bg-gradient-to-r from-primary/5 to-accent/5 rounded-2xl p-8 border border-primary/10\">\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n            Join Our Happy Patients\n          </h3>\n          <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n            Experience the same exceptional care that our patients rave about. \n            Book your appointment today and see why we're Biratnagar's trusted dental clinic.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button asChild size=\"lg\">\n              <Link href=\"/contact\">Book Your Appointment</Link>\n            </Button>\n            <Button asChild variant=\"outline\" size=\"lg\">\n              <Link href=\"/testimonials\">\n                Read More Reviews\n                <ArrowRight className=\"ml-2 h-4 w-4\" />\n              </Link>\n            </Button>\n          </div>\n        </div>\n      </motion.div>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,iDAAiD;IACjD,MAAM,uBAAuB,uHAAA,CAAA,eAAY,CAAC,KAAK,CAAC,GAAG;IAEnD,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,YAAW;;0BAClB,8OAAC,mIAAA,CAAA,gBAAa;gBACZ,UAAS;gBACT,OAAM;gBACN,aAAY;;;;;;0BAId,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,UAAU;oBAAE,MAAM;gBAAK;gBACvB,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,8OAAC,kMAAA,CAAA,OAAI;oCAAY,WAAU;mCAAhB;;;;;;;;;;sCAGf,8OAAC;4BAAK,WAAU;sCAAsC;;;;;;sCACtD,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAKpC,8OAAC;gBAAI,WAAU;0BACZ,qBAAqB,GAAG,CAAC,CAAC,aAAa,sBACtC,8OAAC,+IAAA,CAAA,UAAe;wBAEd,aAAa;wBACb,OAAO;wBACP,aAAa;uBAHR,YAAY,EAAE;;;;;;;;;;0BASzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,UAAU;oBAAE,MAAM;gBAAK;gBACvB,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAuC;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,MAAK;8CACnB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAW;;;;;;;;;;;8CAExB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,SAAQ;oCAAU,MAAK;8CACrC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;4CAAgB;0DAEzB,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC", "debugId": null}}, {"offset": {"line": 1810, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/dental/dental-clinic/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1834, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/dental/dental-clinic/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1857, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/dental/dental-clinic/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1883, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/dental/dental-clinic/src/components/ui/contact-form.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Label } from '@/components/ui/label';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\n\ninterface ContactFormProps {\n  title?: string;\n  showCard?: boolean;\n}\n\nexport default function ContactForm({ \n  title = \"Send us a Message\", \n  showCard = true \n}: ContactFormProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    subject: '',\n    message: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitted, setSubmitted] = useState(false);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData(prev => ({\n      ...prev,\n      [e.target.name]: e.target.value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    \n    // Simulate form submission\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    \n    setIsSubmitting(false);\n    setSubmitted(true);\n    \n    // Reset form after 3 seconds\n    setTimeout(() => {\n      setSubmitted(false);\n      setFormData({\n        name: '',\n        email: '',\n        phone: '',\n        subject: '',\n        message: ''\n      });\n    }, 3000);\n  };\n\n  const FormContent = () => (\n    <div className=\"space-y-6\">\n      {title && (\n        <div className=\"text-center\">\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">{title}</h3>\n          <p className=\"text-gray-600\">We'd love to hear from you. Send us a message and we'll respond as soon as possible.</p>\n        </div>\n      )}\n      \n      {submitted ? (\n        <motion.div\n          initial={{ opacity: 0, scale: 0.9 }}\n          animate={{ opacity: 1, scale: 1 }}\n          className=\"text-center py-8\"\n        >\n          <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <svg className=\"w-8 h-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n            </svg>\n          </div>\n          <h4 className=\"text-xl font-semibold text-gray-900 mb-2\">Message Sent!</h4>\n          <p className=\"text-gray-600\">Thank you for contacting us. We'll get back to you soon.</p>\n        </motion.div>\n      ) : (\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"name\">Full Name *</Label>\n              <Input\n                id=\"name\"\n                name=\"name\"\n                type=\"text\"\n                required\n                value={formData.name}\n                onChange={handleChange}\n                placeholder=\"Your full name\"\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"email\">Email Address *</Label>\n              <Input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                required\n                value={formData.email}\n                onChange={handleChange}\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"phone\">Phone Number</Label>\n              <Input\n                id=\"phone\"\n                name=\"phone\"\n                type=\"tel\"\n                value={formData.phone}\n                onChange={handleChange}\n                placeholder=\"+977-XXX-XXXXXX\"\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"subject\">Subject *</Label>\n              <Input\n                id=\"subject\"\n                name=\"subject\"\n                type=\"text\"\n                required\n                value={formData.subject}\n                onChange={handleChange}\n                placeholder=\"What is this regarding?\"\n              />\n            </div>\n          </div>\n          \n          <div className=\"space-y-2\">\n            <Label htmlFor=\"message\">Message *</Label>\n            <Textarea\n              id=\"message\"\n              name=\"message\"\n              required\n              rows={5}\n              value={formData.message}\n              onChange={handleChange}\n              placeholder=\"Tell us how we can help you...\"\n            />\n          </div>\n          \n          <Button \n            type=\"submit\" \n            className=\"w-full\" \n            disabled={isSubmitting}\n          >\n            {isSubmitting ? 'Sending...' : 'Send Message'}\n          </Button>\n        </form>\n      )}\n    </div>\n  );\n\n  if (showCard) {\n    return (\n      <Card>\n        <CardContent className=\"p-6\">\n          <FormContent />\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return <FormContent />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAee,SAAS,YAAY,EAClC,QAAQ,mBAAmB,EAC3B,WAAW,IAAI,EACE;IACjB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,CAAC;QACpB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;YACjC,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,gBAAgB;QAChB,aAAa;QAEb,6BAA6B;QAC7B,WAAW;YACT,aAAa;YACb,YAAY;gBACV,MAAM;gBACN,OAAO;gBACP,OAAO;gBACP,SAAS;gBACT,SAAS;YACX;QACF,GAAG;IACL;IAEA,MAAM,cAAc,kBAClB,8OAAC;YAAI,WAAU;;gBACZ,uBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;gBAIhC,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAAyB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAChF,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;yCAG/B,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAO;;;;;;sDACtB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,IAAI;4CACpB,UAAU;4CACV,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;sCAKlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,aAAY;;;;;;;;;;;;8CAGhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAU;;;;;;sDACzB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,OAAO;4CACvB,UAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;sCAKlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAU;;;;;;8CACzB,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,MAAK;oCACL,QAAQ;oCACR,MAAM;oCACN,OAAO,SAAS,OAAO;oCACvB,UAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;sCAET,eAAe,eAAe;;;;;;;;;;;;;;;;;;IAOzC,IAAI,UAAU;QACZ,qBACE,8OAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;;;;;;;;;;;;;;;IAIT;IAEA,qBAAO,8OAAC;;;;;AACV", "debugId": null}}, {"offset": {"line": 2248, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/dental/dental-clinic/src/components/sections/ContactSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Phone, Mail, MapPin, Clock } from 'lucide-react';\nimport { Section, SectionHeader } from '@/components/ui/section';\nimport ContactForm from '@/components/ui/contact-form';\nimport { contactInfo } from '@/data/mockData';\n\nexport default function ContactSection() {\n  return (\n    <Section background=\"gray\">\n      <SectionHeader\n        subtitle=\"Get In Touch\"\n        title=\"Contact Us Today\"\n        description=\"Ready to schedule your appointment? Get in touch with us and we'll help you achieve the smile you've always wanted.\"\n      />\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n        {/* Contact Information */}\n        <motion.div\n          initial={{ opacity: 0, x: -50 }}\n          whileInView={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"space-y-8\"\n        >\n          {/* Contact Details */}\n          <div className=\"bg-white rounded-2xl p-8 shadow-sm border border-gray-100\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">Visit Our Clinic</h3>\n            \n            <div className=\"space-y-6\">\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0\">\n                  <MapPin className=\"h-6 w-6 text-primary\" />\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-1\">Address</h4>\n                  <p className=\"text-gray-600\">{contactInfo.address}</p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0\">\n                  <Phone className=\"h-6 w-6 text-primary\" />\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-1\">Phone</h4>\n                  <p className=\"text-gray-600\">{contactInfo.phone}</p>\n                  <p className=\"text-sm text-gray-500\">Emergency: {contactInfo.emergencyPhone}</p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0\">\n                  <Mail className=\"h-6 w-6 text-primary\" />\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-1\">Email</h4>\n                  <p className=\"text-gray-600\">{contactInfo.email}</p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0\">\n                  <Clock className=\"h-6 w-6 text-primary\" />\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-1\">Business Hours</h4>\n                  <div className=\"text-gray-600 text-sm space-y-1\">\n                    {Object.entries(contactInfo.businessHours).map(([day, hours]) => (\n                      <div key={day} className=\"flex justify-between\">\n                        <span className=\"font-medium\">{day}:</span>\n                        <span>{hours}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Google Maps */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"bg-white rounded-2xl overflow-hidden shadow-sm border border-gray-100\"\n          >\n            <div className=\"h-64 bg-gray-200 relative\">\n              <iframe\n                src={contactInfo.mapEmbedUrl}\n                width=\"100%\"\n                height=\"100%\"\n                style={{ border: 0 }}\n                allowFullScreen\n                loading=\"lazy\"\n                referrerPolicy=\"no-referrer-when-downgrade\"\n                title=\"Dental Care Clinic Biratnagar Location\"\n                className=\"absolute inset-0\"\n              />\n            </div>\n            <div className=\"p-4\">\n              <p className=\"text-sm text-gray-600\">\n                Located in the heart of Biratnagar, easily accessible by public transport and with parking available.\n              </p>\n            </div>\n          </motion.div>\n        </motion.div>\n\n        {/* Contact Form */}\n        <motion.div\n          initial={{ opacity: 0, x: 50 }}\n          whileInView={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <ContactForm title=\"Send us a Message\" showCard={true} />\n        </motion.div>\n      </div>\n    </Section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,YAAW;;0BAClB,8OAAC,mIAAA,CAAA,gBAAa;gBACZ,UAAS;gBACT,OAAM;gBACN,aAAY;;;;;;0BAGd,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAGV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAEtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAEpB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAiB,uHAAA,CAAA,cAAW,CAAC,OAAO;;;;;;;;;;;;;;;;;;0DAIrD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAiB,uHAAA,CAAA,cAAW,CAAC,KAAK;;;;;;0EAC/C,8OAAC;gEAAE,WAAU;;oEAAwB;oEAAY,uHAAA,CAAA,cAAW,CAAC,cAAc;;;;;;;;;;;;;;;;;;;0DAI/E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAiB,uHAAA,CAAA,cAAW,CAAC,KAAK;;;;;;;;;;;;;;;;;;0DAInD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,8OAAC;gEAAI,WAAU;0EACZ,OAAO,OAAO,CAAC,uHAAA,CAAA,cAAW,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC1D,8OAAC;wEAAc,WAAU;;0FACvB,8OAAC;gFAAK,WAAU;;oFAAe;oFAAI;;;;;;;0FACnC,8OAAC;0FAAM;;;;;;;uEAFC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAYtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,KAAK,uHAAA,CAAA,cAAW,CAAC,WAAW;4CAC5B,OAAM;4CACN,QAAO;4CACP,OAAO;gDAAE,QAAQ;4CAAE;4CACnB,eAAe;4CACf,SAAQ;4CACR,gBAAe;4CACf,OAAM;4CACN,WAAU;;;;;;;;;;;kDAGd,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAQ3C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;kCAEvB,cAAA,8OAAC,2IAAA,CAAA,UAAW;4BAAC,OAAM;4BAAoB,UAAU;;;;;;;;;;;;;;;;;;;;;;;AAK3D", "debugId": null}}]}