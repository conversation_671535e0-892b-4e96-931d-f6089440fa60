'use client';

import { motion } from 'framer-motion';
import { Phone, Mail, MapPin, Clock } from 'lucide-react';
import { Section } from '@/components/ui/section';
import { Button } from '@/components/ui/button';
import { contactInfo } from '@/data/mockData';

const quickContactOptions = [
  {
    icon: Phone,
    title: 'Call Us',
    value: contactInfo.phone,
    description: 'Speak with our friendly staff',
    action: `tel:${contactInfo.phone}`,
    color: 'bg-green-500'
  },
  {
    icon: Mail,
    title: 'Email Us',
    value: contactInfo.email,
    description: 'Send us your questions',
    action: `mailto:${contactInfo.email}`,
    color: 'bg-blue-500'
  },
  {
    icon: MapPin,
    title: 'Visit Us',
    value: 'Biratnagar, Nepal',
    description: 'Find our clinic location',
    action: '#location',
    color: 'bg-purple-500'
  },
  {
    icon: Clock,
    title: 'Emergency',
    value: contactInfo.emergencyPhone,
    description: '24/7 emergency care',
    action: `tel:${contactInfo.emergencyPhone}`,
    color: 'bg-red-500'
  }
];

export default function ContactHero() {
  return (
    <Section background="gray" className="pt-32 pb-20">
      <div className="text-center max-w-4xl mx-auto mb-16">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <p className="text-primary font-semibold text-sm uppercase tracking-wide mb-4">
            Get In Touch
          </p>
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
            Contact{' '}
            <span className="text-primary">Our Team</span>
          </h1>
          <p className="text-xl text-gray-600 leading-relaxed">
            Ready to schedule your appointment or have questions about our services? 
            We're here to help and look forward to hearing from you.
          </p>
        </motion.div>
      </div>

      {/* Quick Contact Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickContactOptions.map((option, index) => (
          <motion.div
            key={option.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
          >
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center hover:shadow-md transition-shadow duration-300 group">
              <div className={`w-16 h-16 ${option.color} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                <option.icon className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {option.title}
              </h3>
              <p className="text-primary font-semibold mb-2">
                {option.value}
              </p>
              <p className="text-gray-600 text-sm mb-4">
                {option.description}
              </p>
              <Button
                asChild
                variant="outline"
                size="sm"
                className="w-full"
              >
                <a href={option.action}>
                  Contact Now
                </a>
              </Button>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Business Hours Highlight */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
        className="mt-16 text-center"
      >
        <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 max-w-2xl mx-auto">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            We're Open Today
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="font-semibold text-gray-900">Regular Hours:</p>
              <p className="text-gray-600">Monday - Friday: 10:00 AM - 6:00 PM</p>
              <p className="text-gray-600">Sunday: 10:00 AM - 6:00 PM</p>
            </div>
            <div>
              <p className="font-semibold text-gray-900">Emergency Care:</p>
              <p className="text-gray-600">24/7 Emergency Services</p>
              <p className="text-primary font-semibold">{contactInfo.emergencyPhone}</p>
            </div>
          </div>
        </div>
      </motion.div>
    </Section>
  );
}
