import type { <PERSON>ada<PERSON> } from "next";
import { Poppins } from "next/font/google";
import "./globals.css";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { LocalBusinessStructuredData, MedicalOrganizationStructuredData } from "@/components/seo/StructuredData";

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "Dental Care Clinic Biratnagar | Best Dentist in Biratnagar, Nepal",
  description: "Professional dental care services in Biratnagar, Nepal. Expert dentists providing teeth cleaning, whitening, orthodontics, implants and comprehensive oral health care.",
  keywords: "dental clinic Biratnagar, best dentist Biratnagar, teeth whitening Biratnagar, dental care Nepal, orthodontics Biratnagar, dental implants",
  authors: [{ name: "Dental Care Clinic Biratnagar" }],
  openGraph: {
    title: "Dental Care Clinic Biratnagar | Best Dentist in Biratnagar, Nepal",
    description: "Professional dental care services in Biratnagar, Nepal. Expert dentists providing comprehensive oral health care.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Dental Care Clinic Biratnagar | Best Dentist in Biratnagar, Nepal",
    description: "Professional dental care services in Biratnagar, Nepal.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <LocalBusinessStructuredData />
        <MedicalOrganizationStructuredData />
      </head>
      <body
        className={`${poppins.variable} font-poppins antialiased bg-white text-gray-900`}
      >
        <Header />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
