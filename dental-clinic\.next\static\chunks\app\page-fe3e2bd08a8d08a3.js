(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{224:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});let i=(0,a(9946).A)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]])},1637:(e,s,a)=>{"use strict";a.d(s,{A:()=>d});var i=a(5155),t=a(2605),r=a(8564),l=a(224),n=a(6695),c=a(6126);function d(e){let s,{testimonial:a,index:d=0,showService:o=!0}=e;return(0,i.jsx)(t.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*d},children:(0,i.jsx)(n.Zp,{className:"h-full hover:shadow-lg transition-shadow duration-300",children:(0,i.jsxs)(n.Wu,{className:"p-6",children:[(0,i.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,i.jsx)(l.A,{className:"h-8 w-8 text-primary opacity-50"}),(0,i.jsx)("div",{className:"flex space-x-1",children:(s=a.rating,Array.from({length:5},(e,a)=>(0,i.jsx)(r.A,{className:"h-4 w-4 ".concat(a<s?"text-yellow-400 fill-current":"text-gray-300")},a)))})]}),(0,i.jsxs)("blockquote",{className:"text-gray-700 mb-6 leading-relaxed",children:['"',a.review,'"']}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-full overflow-hidden",children:(0,i.jsx)("img",{src:a.image||"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",alt:a.name,className:"w-full h-full object-cover",onError:e=>{e.target.src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"}})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-semibold text-gray-900",children:a.name}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:a.city})]})]}),o&&(0,i.jsx)(c.E,{variant:"outline",className:"text-xs",children:a.service})]}),(0,i.jsx)("div",{className:"mt-4 text-xs text-gray-500",children:new Date(a.date).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]})})})}},2072:(e,s,a)=>{"use strict";a.d(s,{A:()=>u});var i=a(5155),t=a(2115),r=a(2605),l=a(285),n=a(9434);function c(e){let{className:s,type:a,...t}=e;return(0,i.jsx)("input",{type:a,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...t})}function d(e){let{className:s,...a}=e;return(0,i.jsx)("textarea",{"data-slot":"textarea",className:(0,n.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),...a})}a(7650);var o=a(4624),x=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let a=(0,o.TL)(`Primitive.${s}`),r=t.forwardRef((e,t)=>{let{asChild:r,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(r?a:s,{...l,ref:t})});return r.displayName=`Primitive.${s}`,{...e,[s]:r}},{}),m=t.forwardRef((e,s)=>(0,i.jsx)(x.label,{...e,ref:s,onMouseDown:s=>{var a;s.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));function h(e){let{className:s,...a}=e;return(0,i.jsx)(m,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}m.displayName="Label";var p=a(6695);function u(e){let{title:s="Send us a Message",showCard:a=!0}=e,[n,o]=(0,t.useState)({name:"",email:"",phone:"",subject:"",message:""}),[x,m]=(0,t.useState)(!1),[u,y]=(0,t.useState)(!1),g=e=>{o(s=>({...s,[e.target.name]:e.target.value}))},j=async e=>{e.preventDefault(),m(!0),await new Promise(e=>setTimeout(e,1e3)),m(!1),y(!0),setTimeout(()=>{y(!1),o({name:"",email:"",phone:"",subject:"",message:""})},3e3)},f=()=>(0,i.jsxs)("div",{className:"space-y-6",children:[s&&(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:s}),(0,i.jsx)("p",{className:"text-gray-600",children:"We'd love to hear from you. Send us a message and we'll respond as soon as possible."})]}),u?(0,i.jsxs)(r.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"text-center py-8",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,i.jsx)("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,i.jsx)("h4",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Message Sent!"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Thank you for contacting us. We'll get back to you soon."})]}):(0,i.jsxs)("form",{onSubmit:j,className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(h,{htmlFor:"name",children:"Full Name *"}),(0,i.jsx)(c,{id:"name",name:"name",type:"text",required:!0,value:n.name,onChange:g,placeholder:"Your full name"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(h,{htmlFor:"email",children:"Email Address *"}),(0,i.jsx)(c,{id:"email",name:"email",type:"email",required:!0,value:n.email,onChange:g,placeholder:"<EMAIL>"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(h,{htmlFor:"phone",children:"Phone Number"}),(0,i.jsx)(c,{id:"phone",name:"phone",type:"tel",value:n.phone,onChange:g,placeholder:"+977-XXX-XXXXXX"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(h,{htmlFor:"subject",children:"Subject *"}),(0,i.jsx)(c,{id:"subject",name:"subject",type:"text",required:!0,value:n.subject,onChange:g,placeholder:"What is this regarding?"})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(h,{htmlFor:"message",children:"Message *"}),(0,i.jsx)(d,{id:"message",name:"message",required:!0,rows:5,value:n.message,onChange:g,placeholder:"Tell us how we can help you..."})]}),(0,i.jsx)(l.$,{type:"submit",className:"w-full",disabled:x,children:x?"Sending...":"Send Message"})]})]});return a?(0,i.jsx)(p.Zp,{children:(0,i.jsx)(p.Wu,{className:"p-6",children:(0,i.jsx)(f,{})})}):(0,i.jsx)(f,{})}},3076:(e,s,a)=>{"use strict";a.d(s,{default:()=>h});var i=a(5155),t=a(2605),r=a(6874),l=a.n(r),n=a(8564),c=a(2138),d=a(4090),o=a(285),x=a(1637),m=a(8720);function h(){let e=m.rR.slice(0,3);return(0,i.jsxs)(d.w,{background:"white",children:[(0,i.jsx)(d.X,{subtitle:"Patient Reviews",title:"What Our Patients Say",description:"Don't just take our word for it. Here's what our patients have to say about their experience at our clinic."}),(0,i.jsx)(t.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-12",children:(0,i.jsxs)("div",{className:"inline-flex items-center space-x-2 bg-yellow-50 px-6 py-3 rounded-full",children:[(0,i.jsx)("div",{className:"flex space-x-1",children:[1,2,3,4,5].map(e=>(0,i.jsx)(n.A,{className:"h-5 w-5 text-yellow-400 fill-current"},e))}),(0,i.jsx)("span",{className:"text-lg font-semibold text-gray-900",children:"4.9/5"}),(0,i.jsx)("span",{className:"text-gray-600",children:"from 500+ reviews"})]})}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12",children:e.map((e,s)=>(0,i.jsx)(x.A,{testimonial:e,index:s,showService:!0},e.id))}),(0,i.jsx)(t.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center",children:(0,i.jsxs)("div",{className:"bg-gradient-to-r from-primary/5 to-accent/5 rounded-2xl p-8 border border-primary/10",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Join Our Happy Patients"}),(0,i.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Experience the same exceptional care that our patients rave about. Book your appointment today and see why we're Biratnagar's trusted dental clinic."}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,i.jsx)(o.$,{asChild:!0,size:"lg",children:(0,i.jsx)(l(),{href:"/contact",children:"Book Your Appointment"})}),(0,i.jsx)(o.$,{asChild:!0,variant:"outline",size:"lg",children:(0,i.jsxs)(l(),{href:"/testimonials",children:["Read More Reviews",(0,i.jsx)(c.A,{className:"ml-2 h-4 w-4"})]})})]})]})})]})}},4186:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});let i=(0,a(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4516:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});let i=(0,a(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4545:(e,s,a)=>{Promise.resolve().then(a.bind(a,7602)),Promise.resolve().then(a.bind(a,6132)),Promise.resolve().then(a.bind(a,7652)),Promise.resolve().then(a.bind(a,7366)),Promise.resolve().then(a.bind(a,3076))},5525:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});let i=(0,a(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},6132:(e,s,a)=>{"use strict";a.d(s,{default:()=>m});var i=a(5155),t=a(2605),r=a(6874),l=a.n(r),n=a(285),c=a(9420),d=a(4516),o=a(4186),x=a(8720);function m(){return(0,i.jsxs)("section",{className:"relative bg-gradient-to-br from-blue-50 via-white to-teal-50 py-20 sm:py-32 overflow-hidden",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] -z-10"}),(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,i.jsxs)(t.P.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},className:"space-y-8",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)(t.P.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight",children:["Your Smile,"," ",(0,i.jsx)("span",{className:"text-primary",children:"Our Priority"})]}),(0,i.jsx)(t.P.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"text-xl text-gray-600 leading-relaxed max-w-2xl",children:"Experience exceptional dental care in the heart of Biratnagar. Our expert team provides comprehensive dental services with the latest technology and a gentle touch."})]}),(0,i.jsxs)(t.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"grid grid-cols-1 sm:grid-cols-3 gap-4 py-6",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[(0,i.jsx)("div",{className:"w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center",children:(0,i.jsx)(c.A,{className:"h-5 w-5 text-primary"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-semibold text-gray-900",children:"Call Us"}),(0,i.jsx)("p",{className:"text-gray-600",children:x.r_.phone})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[(0,i.jsx)("div",{className:"w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center",children:(0,i.jsx)(d.A,{className:"h-5 w-5 text-primary"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-semibold text-gray-900",children:"Location"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Biratnagar, Nepal"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-3 text-sm",children:[(0,i.jsx)("div",{className:"w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center",children:(0,i.jsx)(o.A,{className:"h-5 w-5 text-primary"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-semibold text-gray-900",children:"Open Today"}),(0,i.jsx)("p",{className:"text-gray-600",children:"10:00 AM - 6:00 PM"})]})]})]}),(0,i.jsxs)(t.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"flex flex-col sm:flex-row gap-4",children:[(0,i.jsx)(n.$,{asChild:!0,size:"lg",className:"text-lg px-8 py-6",children:(0,i.jsx)(l(),{href:"/contact",children:"Book Appointment"})}),(0,i.jsx)(n.$,{asChild:!0,variant:"outline",size:"lg",className:"text-lg px-8 py-6",children:(0,i.jsx)(l(),{href:"/services",children:"Our Services"})})]})]}),(0,i.jsx)(t.P.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.4},className:"relative",children:(0,i.jsxs)("div",{className:"relative w-full h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl",children:[(0,i.jsx)("img",{src:"https://images.unsplash.com/photo-1629909613654-28e377c37b09?w=600&h=500&fit=crop&crop=center",alt:"Modern dental clinic interior",className:"w-full h-full object-cover"}),(0,i.jsx)(t.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.8,delay:1.2},className:"absolute bottom-6 left-6 right-6 bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-semibold text-gray-900",children:"Expert Care Since 2015"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"Trusted by 5000+ patients"})]}),(0,i.jsxs)("div",{className:"flex -space-x-2",children:[[1,2,3].map(e=>(0,i.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full border-2 border-white",style:{backgroundImage:"url(https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face)",backgroundSize:"cover"}},e)),(0,i.jsx)("div",{className:"w-8 h-8 bg-primary rounded-full border-2 border-white flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-xs text-white font-semibold",children:"+"})})]})]})})]})})]})})]})}},7366:(e,s,a)=>{"use strict";a.d(s,{default:()=>m});var i=a(5155),t=a(2605),r=a(6874),l=a.n(r),n=a(2138),c=a(4090),d=a(285),o=a(8673),x=a(8720);function m(){let e=x.$p.slice(0,6);return(0,i.jsxs)(c.w,{background:"gray",children:[(0,i.jsx)(c.X,{subtitle:"Our Services",title:"Comprehensive Dental Care",description:"We offer a full range of dental services to keep your smile healthy and beautiful. From routine cleanings to advanced procedures, our expert team is here to help."}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12",children:e.map((e,s)=>(0,i.jsx)(o.A,{service:e,index:s,showPrice:!1,showFullDescription:!1},e.id))}),(0,i.jsx)(t.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center",children:(0,i.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Need a Specific Treatment?"}),(0,i.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Can't find what you're looking for? We offer many more specialized dental services. Contact us to discuss your specific needs or browse our complete service list."}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,i.jsx)(d.$,{asChild:!0,size:"lg",children:(0,i.jsxs)(l(),{href:"/services",children:["View All Services",(0,i.jsx)(n.A,{className:"ml-2 h-4 w-4"})]})}),(0,i.jsx)(d.$,{asChild:!0,variant:"outline",size:"lg",children:(0,i.jsx)(l(),{href:"/contact",children:"Schedule Consultation"})})]})]})})]})}},7602:(e,s,a)=>{"use strict";a.d(s,{default:()=>m});var i=a(5155),t=a(2605),r=a(4516),l=a(9420),n=a(8883),c=a(4186),d=a(4090),o=a(2072),x=a(8720);function m(){return(0,i.jsxs)(d.w,{background:"gray",children:[(0,i.jsx)(d.X,{subtitle:"Get In Touch",title:"Contact Us Today",description:"Ready to schedule your appointment? Get in touch with us and we'll help you achieve the smile you've always wanted."}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,i.jsxs)(t.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-8",children:[(0,i.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Visit Our Clinic"}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,i.jsx)(r.A,{className:"h-6 w-6 text-primary"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-900 mb-1",children:"Address"}),(0,i.jsx)("p",{className:"text-gray-600",children:x.r_.address})]})]}),(0,i.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,i.jsx)(l.A,{className:"h-6 w-6 text-primary"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-900 mb-1",children:"Phone"}),(0,i.jsx)("p",{className:"text-gray-600",children:x.r_.phone}),(0,i.jsxs)("p",{className:"text-sm text-gray-500",children:["Emergency: ",x.r_.emergencyPhone]})]})]}),(0,i.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,i.jsx)(n.A,{className:"h-6 w-6 text-primary"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-900 mb-1",children:"Email"}),(0,i.jsx)("p",{className:"text-gray-600",children:x.r_.email})]})]}),(0,i.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,i.jsx)(c.A,{className:"h-6 w-6 text-primary"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-900 mb-1",children:"Business Hours"}),(0,i.jsx)("div",{className:"text-gray-600 text-sm space-y-1",children:Object.entries(x.r_.businessHours).map(e=>{let[s,a]=e;return(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsxs)("span",{className:"font-medium",children:[s,":"]}),(0,i.jsx)("span",{children:a})]},s)})})]})]})]})]}),(0,i.jsxs)(t.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"bg-white rounded-2xl overflow-hidden shadow-sm border border-gray-100",children:[(0,i.jsx)("div",{className:"h-64 bg-gray-200 relative",children:(0,i.jsx)("iframe",{src:x.r_.mapEmbedUrl,width:"100%",height:"100%",style:{border:0},allowFullScreen:!0,loading:"lazy",referrerPolicy:"no-referrer-when-downgrade",title:"Dental Care Clinic Biratnagar Location",className:"absolute inset-0"})}),(0,i.jsx)("div",{className:"p-4",children:(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"Located in the heart of Biratnagar, easily accessible by public transport and with parking available."})})]})]}),(0,i.jsx)(t.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},children:(0,i.jsx)(o.A,{title:"Send us a Message",showCard:!0})})]})]})}},7652:(e,s,a)=>{"use strict";a.d(s,{default:()=>m});var i=a(5155),t=a(2605),r=a(1976),l=a(5525),n=a(7580),c=a(9037),d=a(4090),o=a(8720);let x=[{icon:r.A,title:"Compassionate Care",description:"We treat every patient with kindness, understanding, and respect, ensuring a comfortable experience."},{icon:l.A,title:"Advanced Technology",description:"State-of-the-art equipment and modern techniques for precise, effective dental treatments."},{icon:n.A,title:"Expert Team",description:"Highly qualified dentists and staff with years of experience in comprehensive dental care."},{icon:c.A,title:"Quality Assurance",description:"Committed to maintaining the highest standards of dental care and patient satisfaction."}];function m(){return(0,i.jsx)(d.w,{background:"white",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center",children:[(0,i.jsxs)(t.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-8",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("p",{className:"text-primary font-semibold text-sm uppercase tracking-wide",children:"About Our Clinic"}),(0,i.jsxs)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900",children:["Dedicated to Your"," ",(0,i.jsx)("span",{className:"text-primary",children:"Oral Health"})]}),(0,i.jsx)("p",{className:"text-lg text-gray-600 leading-relaxed",children:o.x0.mission}),(0,i.jsx)("p",{className:"text-gray-600 leading-relaxed",children:o.x0.story})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,i.jsxs)(t.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},className:"text-center",children:[(0,i.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:"5000+"}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Happy Patients"})]}),(0,i.jsxs)(t.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},viewport:{once:!0},className:"text-center",children:[(0,i.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:"9+"}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Years Experience"})]}),(0,i.jsxs)(t.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.4},viewport:{once:!0},className:"text-center",children:[(0,i.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:"15+"}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Services Offered"})]}),(0,i.jsxs)(t.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.5},viewport:{once:!0},className:"text-center",children:[(0,i.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:"98%"}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:"Satisfaction Rate"})]})]})]}),(0,i.jsx)(t.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:x.map((e,s)=>(0,i.jsxs)(t.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*s},viewport:{once:!0},className:"bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4",children:(0,i.jsx)(e.icon,{className:"h-6 w-6 text-primary"})}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.title}),(0,i.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:e.description})]},e.title))})]})})}},8564:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});let i=(0,a(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},8673:(e,s,a)=>{"use strict";a.d(s,{A:()=>n});var i=a(5155),t=a(2605),r=a(6695),l=a(6126);function n(e){let{service:s,index:a=0,showPrice:n=!1,showFullDescription:c=!1}=e;return(0,i.jsx)(t.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*a},children:(0,i.jsxs)(r.Zp,{className:"h-full hover:shadow-lg transition-shadow duration-300",children:[(0,i.jsxs)(r.aR,{children:[(0,i.jsx)("div",{className:"w-full h-48 bg-gray-200 rounded-lg mb-4 overflow-hidden",children:(0,i.jsx)("img",{src:s.image,alt:s.name,className:"w-full h-full object-cover",onError:e=>{e.target.src="https://images.unsplash.com/photo-1606811841689-23dfddce3e95?w=400&h=300&fit=crop&crop=center"}})}),(0,i.jsx)(r.ZB,{className:"text-xl font-bold text-gray-900",children:s.name}),n&&s.price&&(0,i.jsx)(l.E,{variant:"secondary",className:"w-fit",children:s.price})]}),(0,i.jsxs)(r.Wu,{children:[(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:s.description}),c&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Benefits:"}),(0,i.jsx)("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-600",children:s.benefits.map((e,s)=>(0,i.jsx)("li",{children:e},s))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Procedure:"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:s.procedure})]})]})]})]})})}},8883:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});let i=(0,a(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9420:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});let i=(0,a(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])}},e=>{e.O(0,[939,874,343,441,964,358],()=>e(e.s=4545)),_N_E=e.O()}]);