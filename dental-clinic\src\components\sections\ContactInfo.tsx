'use client';

import { motion } from 'framer-motion';
import { MapPin, Phone, Mail, Clock, Car, Bus, Facebook, Instagram, Twitter } from 'lucide-react';
import { Section, SectionHeader } from '@/components/ui/section';
import { Card, CardContent } from '@/components/ui/card';
import { contactInfo } from '@/data/mockData';

const contactDetails = [
  {
    icon: MapPin,
    title: 'Our Address',
    content: contactInfo.address,
    description: 'Located in the heart of Biratnagar for easy access'
  },
  {
    icon: Phone,
    title: 'Phone Numbers',
    content: contactInfo.phone,
    description: `Emergency: ${contactInfo.emergencyPhone}`,
    action: `tel:${contactInfo.phone}`
  },
  {
    icon: Mail,
    title: 'Email Address',
    content: contactInfo.email,
    description: 'We respond within 24 hours',
    action: `mailto:${contactInfo.email}`
  },
  {
    icon: Clock,
    title: 'Business Hours',
    content: 'Monday - Friday: 10:00 AM - 6:00 PM',
    description: 'Sunday: 10:00 AM - 6:00 PM | Saturday: Closed'
  }
];

const transportOptions = [
  {
    icon: Car,
    title: 'By Car',
    description: 'Free parking available for patients'
  },
  {
    icon: Bus,
    title: 'Public Transport',
    description: 'Accessible by local buses and rickshaws'
  }
];

export default function ContactInfo() {
  return (
    <Section background="white">
      <SectionHeader
        subtitle="Contact Information"
        title="How to Reach Us"
        description="Find all the information you need to contact us, visit our clinic, or get emergency dental care."
      />

      {/* Contact Details Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
        {contactDetails.map((detail, index) => (
          <motion.div
            key={detail.title}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            viewport={{ once: true }}
          >
            <Card className="h-full hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                    <detail.icon className="h-6 w-6 text-primary" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {detail.title}
                    </h3>
                    {detail.action ? (
                      <a
                        href={detail.action}
                        className="text-primary font-semibold hover:underline block mb-2"
                      >
                        {detail.content}
                      </a>
                    ) : (
                      <p className="text-gray-900 font-semibold mb-2">
                        {detail.content}
                      </p>
                    )}
                    <p className="text-gray-600 text-sm">
                      {detail.description}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Additional Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Transportation */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-6">
            Getting to Our Clinic
          </h3>
          <div className="space-y-4">
            {transportOptions.map((option) => (
              <div key={option.title} className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                  <option.icon className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">{option.title}</h4>
                  <p className="text-gray-600 text-sm">{option.description}</p>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-semibold text-gray-900 mb-2">Landmark:</h4>
            <p className="text-gray-600 text-sm">
              Near Main Chowk, opposite to City Mall. Look for our blue and white signboard.
            </p>
          </div>
        </motion.div>

        {/* Social Media & Additional Info */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-6">
            Connect With Us
          </h3>
          
          {/* Social Media */}
          <div className="mb-8">
            <h4 className="font-semibold text-gray-900 mb-4">Follow Us</h4>
            <div className="flex space-x-4">
              <a
                href={contactInfo.socialMedia.facebook}
                className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center text-white hover:bg-blue-700 transition-colors"
                aria-label="Facebook"
              >
                <Facebook className="h-6 w-6" />
              </a>
              <a
                href={contactInfo.socialMedia.instagram}
                className="w-12 h-12 bg-pink-600 rounded-lg flex items-center justify-center text-white hover:bg-pink-700 transition-colors"
                aria-label="Instagram"
              >
                <Instagram className="h-6 w-6" />
              </a>
              <a
                href={contactInfo.socialMedia.twitter}
                className="w-12 h-12 bg-blue-400 rounded-lg flex items-center justify-center text-white hover:bg-blue-500 transition-colors"
                aria-label="Twitter"
              >
                <Twitter className="h-6 w-6" />
              </a>
            </div>
          </div>

          {/* Emergency Information */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 className="font-semibold text-red-900 mb-2">Emergency Care</h4>
            <p className="text-red-800 text-sm mb-2">
              For dental emergencies outside business hours, call our emergency line:
            </p>
            <a
              href={`tel:${contactInfo.emergencyPhone}`}
              className="text-red-600 font-bold text-lg hover:underline"
            >
              {contactInfo.emergencyPhone}
            </a>
            <p className="text-red-700 text-xs mt-2">
              Available 24/7 for urgent dental care needs
            </p>
          </div>
        </motion.div>
      </div>
    </Section>
  );
}
