'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import { Section, SectionHeader } from '@/components/ui/section';
import { Button } from '@/components/ui/button';
import ServiceCard from '@/components/ui/service-card';
import { services } from '@/data/mockData';

export default function ServicesOverview() {
  // Show only the first 6 services on homepage
  const featuredServices = services.slice(0, 6);

  return (
    <Section background="gray">
      <SectionHeader
        subtitle="Our Services"
        title="Comprehensive Dental Care"
        description="We offer a full range of dental services to keep your smile healthy and beautiful. From routine cleanings to advanced procedures, our expert team is here to help."
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
        {featuredServices.map((service, index) => (
          <ServiceCard
            key={service.id}
            service={service}
            index={index}
            showPrice={false}
            showFullDescription={false}
          />
        ))}
      </div>

      {/* CTA Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="text-center"
      >
        <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Need a Specific Treatment?
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Can&apos;t find what you&apos;re looking for? We offer many more specialized dental services.
            Contact us to discuss your specific needs or browse our complete service list.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/services">
                View All Services
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link href="/contact">Schedule Consultation</Link>
            </Button>
          </div>
        </div>
      </motion.div>
    </Section>
  );
}
