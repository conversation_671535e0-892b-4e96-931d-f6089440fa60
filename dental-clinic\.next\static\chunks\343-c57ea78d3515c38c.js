"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[343],{285:(e,a,t)=>{t.d(a,{$:()=>d});var i=t(5155);t(2115);var r=t(4624),n=t(2085),s=t(9434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:a,variant:t,size:n,asChild:d=!1,...c}=e,l=d?r.DX:"button";return(0,i.jsx)(l,{"data-slot":"button",className:(0,s.cn)(o({variant:t,size:n,className:a})),...c})}},1976:(e,a,t)=>{t.d(a,{A:()=>i});let i=(0,t(9946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2138:(e,a,t)=>{t.d(a,{A:()=>i});let i=(0,t(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},4090:(e,a,t)=>{t.d(a,{X:()=>s,w:()=>n});var i=t(5155),r=t(9434);function n(e){let{children:a,className:t,id:n,background:s="white"}=e;return(0,i.jsx)("section",{id:n,className:(0,r.cn)("py-16 sm:py-20",{white:"bg-white",gray:"bg-gray-50",primary:"bg-primary text-primary-foreground"}[s],t),children:(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a})})}function s(e){let{title:a,subtitle:t,description:n,centered:s=!0,className:o}=e;return(0,i.jsxs)("div",{className:(0,r.cn)("mb-12",s&&"text-center",o),children:[t&&(0,i.jsx)("p",{className:"text-primary font-semibold text-sm uppercase tracking-wide mb-2",children:t}),(0,i.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:a}),n&&(0,i.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:n})]})}},6126:(e,a,t)=>{t.d(a,{E:()=>d});var i=t(5155);t(2115);var r=t(4624),n=t(2085),s=t(9434);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:a,variant:t,asChild:n=!1,...d}=e,c=n?r.DX:"span";return(0,i.jsx)(c,{"data-slot":"badge",className:(0,s.cn)(o({variant:t}),a),...d})}},6695:(e,a,t)=>{t.d(a,{Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>s});var i=t(5155);t(2115);var r=t(9434);function n(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function s(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function o(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...t})}function d(e){let{className:a,...t}=e;return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...t})}},7580:(e,a,t)=>{t.d(a,{A:()=>i});let i=(0,t(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},8720:(e,a,t)=>{t.d(a,{$p:()=>r,FF:()=>s,rR:()=>n,r_:()=>o,x0:()=>i,x1:()=>d});let i={name:"Dental Care Clinic Biratnagar",tagline:"Your Smile, Our Priority",mission:"To provide exceptional dental care with compassion, using the latest technology and techniques to ensure every patient receives the highest quality treatment in a comfortable, welcoming environment.",story:"Established in 2015, Dental Care Clinic Biratnagar has been serving the community with dedication and excellence. Our clinic was founded with the vision of making quality dental care accessible to everyone in Biratnagar and surrounding areas.",established:"2015",location:"Biratnagar, Nepal"},r=[{id:"1",name:"Dental Cleaning & Checkups",description:"Comprehensive oral examination and professional teeth cleaning to maintain optimal oral health.",benefits:["Prevents cavities and gum disease","Early detection of dental problems","Fresh breath and clean feeling","Maintains overall oral health"],procedure:"Our dental hygienists perform thorough cleaning using ultrasonic scalers and hand instruments, followed by polishing and fluoride treatment.",image:"/images/services/cleaning.jpg",price:"NPR 1,500 - 2,500"},{id:"2",name:"Teeth Whitening",description:"Professional teeth whitening treatments to brighten your smile and boost your confidence.",benefits:["Removes stains and discoloration","Safe and effective results","Boosts self-confidence","Long-lasting whitening effect"],procedure:"We use professional-grade whitening gel applied with custom trays or in-office laser whitening for immediate results.",image:"/images/services/whitening.jpg",price:"NPR 8,000 - 15,000"},{id:"3",name:"Orthodontics",description:"Comprehensive orthodontic treatment including braces and aligners to straighten teeth and correct bite issues.",benefits:["Straighter, more attractive smile","Improved bite function","Better oral hygiene","Enhanced self-esteem"],procedure:"Treatment begins with comprehensive examination, followed by custom treatment plan using traditional braces or clear aligners.",image:"/images/services/orthodontics.jpg",price:"NPR 50,000 - 150,000"},{id:"4",name:"Dental Implants",description:"Permanent tooth replacement solution using titanium implants for missing teeth.",benefits:["Permanent tooth replacement","Natural look and feel","Preserves jawbone structure","No impact on adjacent teeth"],procedure:"Surgical placement of titanium implant into jawbone, followed by healing period and crown placement.",image:"/images/services/implants.jpg",price:"NPR 80,000 - 120,000"},{id:"5",name:"Root Canal Treatment",description:"Advanced endodontic treatment to save infected or severely damaged teeth.",benefits:["Saves natural tooth","Eliminates pain and infection","Prevents further complications","Cost-effective tooth preservation"],procedure:"Removal of infected pulp, cleaning and disinfection of root canals, followed by filling and crown placement.",image:"/images/services/root-canal.jpg",price:"NPR 8,000 - 15,000"},{id:"6",name:"Cosmetic Dentistry",description:"Aesthetic dental treatments including veneers, bonding, and smile makeovers.",benefits:["Enhanced smile appearance","Improved facial aesthetics","Boosted confidence","Customized treatment plans"],procedure:"Comprehensive smile analysis followed by customized treatment using veneers, bonding, or other cosmetic procedures.",image:"/images/services/cosmetic.jpg",price:"NPR 15,000 - 80,000"}],n=[{id:"1",name:"Sita Rai",city:"Biratnagar",rating:5,review:"Excellent service! Dr. Rajesh performed my dental implant surgery with great care. The staff is very professional and the clinic is very clean. Highly recommended!",service:"Dental Implants",date:"2024-01-15",image:"/images/testimonials/sita.jpg"},{id:"2",name:"Ramesh Gurung",city:"Dharan",rating:5,review:"I had my teeth whitening done here and the results are amazing! Dr. Priya explained everything clearly and the treatment was painless. Very satisfied with the service.",service:"Teeth Whitening",date:"2024-02-20",image:"/images/testimonials/ramesh.jpg"},{id:"3",name:"Maya Shrestha",city:"Biratnagar",rating:5,review:"The orthodontic treatment for my daughter was excellent. Dr. Priya was very patient and gentle with her. The braces were fitted perfectly and the results are wonderful.",service:"Orthodontics",date:"2024-03-10",image:"/images/testimonials/maya.jpg"},{id:"4",name:"Bikash Limbu",city:"Itahari",rating:4,review:"Great experience with root canal treatment. Dr. Amit made sure I was comfortable throughout the procedure. The clinic has modern equipment and very hygienic environment.",service:"Root Canal Treatment",date:"2024-03-25",image:"/images/testimonials/bikash.jpg"},{id:"5",name:"Sunita Karki",city:"Biratnagar",rating:5,review:"Regular dental checkups here have kept my teeth healthy. The staff is friendly and the doctors are very knowledgeable. Best dental clinic in Biratnagar!",service:"Dental Cleaning",date:"2024-04-05",image:"/images/testimonials/sunita.jpg"}],s=[{id:"1",src:"/images/gallery/reception.jpg",alt:"Modern reception area",category:"facility",title:"Reception Area",description:"Comfortable and welcoming reception area"},{id:"2",src:"/images/gallery/treatment-room.jpg",alt:"Treatment room with modern equipment",category:"facility",title:"Treatment Room",description:"State-of-the-art treatment rooms"},{id:"3",src:"/images/gallery/dental-chair.jpg",alt:"Modern dental chair",category:"equipment",title:"Dental Chair",description:"Comfortable modern dental chairs"},{id:"4",src:"/images/gallery/xray-machine.jpg",alt:"Digital X-ray machine",category:"equipment",title:"Digital X-Ray",description:"Advanced digital X-ray technology"},{id:"5",src:"/images/gallery/sterilization.jpg",alt:"Sterilization equipment",category:"equipment",title:"Sterilization Unit",description:"Advanced sterilization equipment"},{id:"6",src:"/images/gallery/team-photo.jpg",alt:"Dental team photo",category:"team",title:"Our Team",description:"Professional dental care team"}],o={address:"Main Road, Biratnagar-10, Morang, Nepal",phone:"+977-21-525678",email:"<EMAIL>",emergencyPhone:"+977-9841234567",businessHours:{Sunday:"10:00 AM - 6:00 PM",Monday:"10:00 AM - 6:00 PM",Tuesday:"10:00 AM - 6:00 PM",Wednesday:"10:00 AM - 6:00 PM",Thursday:"10:00 AM - 6:00 PM",Friday:"10:00 AM - 6:00 PM",Saturday:"Closed"},socialMedia:{facebook:"https://facebook.com/dentalcarebiratnagar",instagram:"https://instagram.com/dentalcarebiratnagar",twitter:"https://twitter.com/dentalcarebiratnagar"},mapEmbedUrl:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3571.5234567890123!2d87.2734567890123!3d26.4567890123456!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjbCsDI3JzI0LjQiTiA4N8KwMTYnMjQuNCJF!5e0!3m2!1sen!2snp!4v1234567890123!5m2!1sen!2snp"},d=[{id:"1",name:"Dr. Rajesh Sharma",role:"Chief Dentist & Founder",qualification:"BDS, MDS (Oral & Maxillofacial Surgery)",experience:"12+ years",bio:"Dr. Rajesh Sharma is a highly experienced oral and maxillofacial surgeon with over 12 years of practice. He founded Dental Care Clinic Biratnagar with the vision of providing world-class dental care to the community.",image:"/images/team/dr-rajesh.jpg",specialties:["Oral Surgery","Dental Implants","Complex Extractions","Facial Trauma"]},{id:"2",name:"Dr. Priya Adhikari",role:"Orthodontist",qualification:"BDS, MDS (Orthodontics)",experience:"8+ years",bio:"Dr. Priya Adhikari specializes in orthodontics and has helped hundreds of patients achieve beautiful, straight smiles. She is known for her gentle approach and attention to detail.",image:"/images/team/dr-priya.jpg",specialties:["Braces","Clear Aligners","Pediatric Orthodontics","Bite Correction"]},{id:"3",name:"Dr. Amit Thapa",role:"Endodontist",qualification:"BDS, MDS (Endodontics)",experience:"6+ years",bio:"Dr. Amit Thapa is our endodontic specialist, focusing on root canal treatments and saving natural teeth. His expertise in pain management ensures comfortable treatment experiences.",image:"/images/team/dr-amit.jpg",specialties:["Root Canal Treatment","Endodontic Surgery","Pain Management","Dental Trauma"]}]},9037:(e,a,t)=>{t.d(a,{A:()=>i});let i=(0,t(9946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},9434:(e,a,t)=>{t.d(a,{cn:()=>n});var i=t(2596),r=t(9688);function n(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,i.$)(a))}}}]);