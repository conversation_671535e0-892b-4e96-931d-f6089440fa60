'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { Heart, Users, Shield, Award, ArrowRight } from 'lucide-react';
import { Section, SectionHeader } from '@/components/ui/section';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

const communityValues = [
  {
    icon: Heart,
    title: 'Community Care',
    description: 'We are deeply committed to the health and wellbeing of the Biratnagar community, providing accessible dental care for all.'
  },
  {
    icon: Users,
    title: 'Local Trust',
    description: 'Built strong relationships with local families over the years, becoming their trusted partner in oral health.'
  },
  {
    icon: Shield,
    title: 'Quality Assurance',
    description: 'Maintaining the highest standards of care and safety, ensuring every patient receives exceptional treatment.'
  },
  {
    icon: Award,
    title: 'Professional Excellence',
    description: 'Recognized for our commitment to continuous learning and adopting the latest dental technologies and techniques.'
  }
];

const communityInitiatives = [
  {
    title: 'Free Dental Camps',
    description: 'Regular free dental checkup camps in schools and community centers',
    impact: '500+ people served annually'
  },
  {
    title: 'Oral Health Education',
    description: 'Educational programs about dental hygiene in local schools',
    impact: '20+ schools reached'
  },
  {
    title: 'Emergency Care',
    description: '24/7 emergency dental services for urgent cases',
    impact: 'Always available for emergencies'
  }
];

export default function CommunityTrust() {
  return (
    <Section background="white">
      <SectionHeader
        subtitle="Community Impact"
        title="Trusted by Biratnagar"
        description="We take pride in being an integral part of the Biratnagar community, contributing to the oral health and wellbeing of our neighbors."
      />

      {/* Community Values */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
        {communityValues.map((value, index) => (
          <motion.div
            key={value.title}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            viewport={{ once: true }}
          >
            <Card className="h-full text-center hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <value.icon className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {value.title}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {value.description}
                </p>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Community Initiatives */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="bg-gradient-to-r from-primary/5 to-accent/5 rounded-2xl p-8 border border-primary/10 mb-16"
      >
        <div className="text-center mb-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Our Community Initiatives
          </h3>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Beyond providing excellent dental care, we actively contribute to the health and education of our community through various initiatives.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {communityInitiatives.map((initiative, index) => (
            <motion.div
              key={initiative.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white rounded-xl p-6 shadow-sm"
            >
              <h4 className="text-lg font-semibold text-gray-900 mb-3">
                {initiative.title}
              </h4>
              <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                {initiative.description}
              </p>
              <div className="text-primary font-semibold text-sm">
                {initiative.impact}
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* CTA Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
        className="text-center"
      >
        <div className="bg-gray-50 rounded-2xl p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Join Our Dental Family
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Experience the care and trust that thousands of Biratnagar families have come to rely on. 
            Schedule your appointment today and become part of our dental family.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/contact">Schedule Appointment</Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link href="/services">
                Explore Our Services
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </motion.div>
    </Section>
  );
}
