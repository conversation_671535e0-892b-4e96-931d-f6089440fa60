(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[763],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var i=a(5155);a(2115);var r=a(4624),s=a(2085),n=a(9434);let o=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:s,asChild:l=!1,...c}=e,d=l?r.DX:"button";return(0,i.jsx)(d,{"data-slot":"button",className:(0,n.cn)(o({variant:a,size:s,className:t})),...c})}},286:(e,t,a)=>{"use strict";a.d(t,{default:()=>h});var i=a(5155),r=a(2605),s=a(9074),n=a(9946);let o=(0,n.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var l=a(8313);let c=(0,n.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var d=a(4090);let m=[{icon:s.A,title:"Book Appointment",description:"Schedule your visit through phone, email, or our online booking system. We offer flexible timing to suit your schedule.",step:"01"},{icon:o,title:"Comprehensive Examination",description:"Our dentist will conduct a thorough examination, including X-rays if needed, to assess your oral health.",step:"02"},{icon:l.A,title:"Treatment Planning",description:"We discuss your treatment options, explain procedures, and create a personalized treatment plan that fits your needs.",step:"03"},{icon:c,title:"Quality Treatment",description:"Receive expert dental care using advanced techniques and equipment, ensuring comfortable and effective treatment.",step:"04"}];function h(){return(0,i.jsxs)(d.w,{background:"gray",children:[(0,i.jsx)(d.X,{subtitle:"How It Works",title:"Our Treatment Process",description:"We follow a systematic approach to ensure you receive the best possible dental care from consultation to completion."}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:m.map((e,t)=>(0,i.jsxs)(r.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},viewport:{once:!0},className:"relative",children:[t<m.length-1&&(0,i.jsx)("div",{className:"hidden lg:block absolute top-16 left-full w-full h-0.5 bg-primary/20 z-0"}),(0,i.jsxs)("div",{className:"bg-white rounded-2xl p-6 shadow-sm border border-gray-100 relative z-10 text-center hover:shadow-md transition-shadow duration-300",children:[(0,i.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,i.jsx)("div",{className:"w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold",children:e.step})}),(0,i.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4 mt-4",children:(0,i.jsx)(e.icon,{className:"h-8 w-8 text-primary"})}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:e.title}),(0,i.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:e.description})]})]},e.title))}),(0,i.jsx)(r.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mt-16",children:(0,i.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:[(0,i.jsxs)("div",{className:"text-center mb-8",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Why Choose Our Process?"}),(0,i.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Our systematic approach ensures that every patient receives personalized, high-quality care tailored to their specific needs."})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,i.jsx)(c,{className:"h-6 w-6 text-green-600"})}),(0,i.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Personalized Care"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"Every treatment plan is customized to your unique needs"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,i.jsx)(c,{className:"h-6 w-6 text-blue-600"})}),(0,i.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Transparent Communication"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"Clear explanation of procedures and costs upfront"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,i.jsx)(c,{className:"h-6 w-6 text-purple-600"})}),(0,i.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Quality Assurance"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"Follow-up care to ensure optimal treatment outcomes"})]})]})]})})]})}},1296:(e,t,a)=>{"use strict";a.d(t,{default:()=>m});var i=a(5155),r=a(2605);let s=(0,a(9946).A)("smile",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 14s1.5 2 4 2 4-2 4-2",key:"1y1vjs"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9",key:"yxxnd0"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9",key:"1p4y9e"}]]);var n=a(5525),o=a(1976),l=a(8564),c=a(4090);let d=[{icon:s,title:"Comprehensive Care",description:"Complete range of dental services"},{icon:n.A,title:"Advanced Technology",description:"State-of-the-art equipment"},{icon:o.A,title:"Gentle Approach",description:"Comfortable, pain-free treatments"},{icon:l.A,title:"Expert Team",description:"Experienced dental professionals"}];function m(){return(0,i.jsxs)(c.w,{background:"gray",className:"pt-32 pb-20",children:[(0,i.jsx)("div",{className:"text-center max-w-4xl mx-auto mb-16",children:(0,i.jsxs)(r.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,i.jsx)("p",{className:"text-primary font-semibold text-sm uppercase tracking-wide mb-4",children:"Our Services"}),(0,i.jsxs)("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6",children:["Complete"," ",(0,i.jsx)("span",{className:"text-primary",children:"Dental Care"})," ","Solutions"]}),(0,i.jsx)("p",{className:"text-xl text-gray-600 leading-relaxed",children:"From routine cleanings to advanced procedures, we offer comprehensive dental services to keep your smile healthy, beautiful, and confident."})]})}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:d.map((e,t)=>(0,i.jsxs)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},className:"bg-white rounded-xl p-6 shadow-sm border border-gray-100 text-center hover:shadow-md transition-shadow duration-300",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,i.jsx)(e.icon,{className:"h-8 w-8 text-primary"})}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.title}),(0,i.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]},e.title))})]})}},1703:(e,t,a)=>{Promise.resolve().then(a.bind(a,286)),Promise.resolve().then(a.bind(a,5279)),Promise.resolve().then(a.bind(a,3803)),Promise.resolve().then(a.bind(a,1296))},1976:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(9946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},3803:(e,t,a)=>{"use strict";a.d(t,{default:()=>l});var i=a(5155),r=a(2605),s=a(4090),n=a(8673),o=a(8720);function l(){return(0,i.jsxs)(s.w,{background:"white",children:[(0,i.jsx)(s.X,{subtitle:"What We Offer",title:"Our Dental Services",description:"We provide a comprehensive range of dental services using the latest technology and techniques to ensure the best possible outcomes for our patients."}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:o.$p.map((e,t)=>(0,i.jsx)(n.A,{service:e,index:t,showPrice:!0,showFullDescription:!0},e.id))}),(0,i.jsx)(r.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mt-16 text-center",children:(0,i.jsxs)("div",{className:"bg-gradient-to-r from-primary/5 to-accent/5 rounded-2xl p-8 border border-primary/10",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Don't See What You Need?"}),(0,i.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"We offer many additional specialized dental services not listed here. Contact us to discuss your specific dental needs and we'll be happy to help."}),(0,i.jsxs)("div",{className:"flex flex-wrap justify-center gap-3 text-sm",children:[(0,i.jsx)("span",{className:"bg-white px-4 py-2 rounded-full text-gray-700",children:"Pediatric Dentistry"}),(0,i.jsx)("span",{className:"bg-white px-4 py-2 rounded-full text-gray-700",children:"Oral Surgery"}),(0,i.jsx)("span",{className:"bg-white px-4 py-2 rounded-full text-gray-700",children:"Periodontics"}),(0,i.jsx)("span",{className:"bg-white px-4 py-2 rounded-full text-gray-700",children:"Prosthodontics"}),(0,i.jsx)("span",{className:"bg-white px-4 py-2 rounded-full text-gray-700",children:"Emergency Care"}),(0,i.jsx)("span",{className:"bg-white px-4 py-2 rounded-full text-gray-700",children:"Dental Crowns"})]})]})})]})}},4090:(e,t,a)=>{"use strict";a.d(t,{X:()=>n,w:()=>s});var i=a(5155),r=a(9434);function s(e){let{children:t,className:a,id:s,background:n="white"}=e;return(0,i.jsx)("section",{id:s,className:(0,r.cn)("py-16 sm:py-20",{white:"bg-white",gray:"bg-gray-50",primary:"bg-primary text-primary-foreground"}[n],a),children:(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:t})})}function n(e){let{title:t,subtitle:a,description:s,centered:n=!0,className:o}=e;return(0,i.jsxs)("div",{className:(0,r.cn)("mb-12",n&&"text-center",o),children:[a&&(0,i.jsx)("p",{className:"text-primary font-semibold text-sm uppercase tracking-wide mb-2",children:a}),(0,i.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:t}),s&&(0,i.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:s})]})}},5279:(e,t,a)=>{"use strict";a.d(t,{default:()=>u});var i=a(5155),r=a(2605),s=a(6874),n=a.n(s),o=a(9420),l=a(9074);let c=(0,a(9946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);var d=a(4090),m=a(285),h=a(6695),p=a(8720);function u(){return(0,i.jsx)(d.w,{background:"white",children:(0,i.jsxs)(r.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center",children:[(0,i.jsxs)("div",{className:"bg-gradient-to-r from-primary to-accent text-white rounded-2xl p-12 mb-12",children:[(0,i.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold mb-4",children:"Ready to Transform Your Smile?"}),(0,i.jsx)("p",{className:"text-xl opacity-90 mb-8 max-w-2xl mx-auto",children:"Don't wait to get the dental care you deserve. Book your appointment today and take the first step towards a healthier, more confident smile."}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,i.jsx)(m.$,{asChild:!0,size:"lg",variant:"secondary",className:"text-lg px-8 py-6",children:(0,i.jsx)(n(),{href:"/contact",children:"Book Appointment Now"})}),(0,i.jsx)(m.$,{asChild:!0,size:"lg",variant:"outline",className:"text-lg px-8 py-6 border-white text-white hover:bg-white hover:text-primary",children:(0,i.jsx)(n(),{href:"tel:{contactInfo.phone}",children:"Call Us Today"})})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,i.jsx)(r.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1},viewport:{once:!0},children:(0,i.jsx)(h.Zp,{className:"hover:shadow-lg transition-shadow duration-300",children:(0,i.jsxs)(h.Wu,{className:"p-6 text-center",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,i.jsx)(o.A,{className:"h-8 w-8 text-primary"})}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Call Us"}),(0,i.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Speak directly with our friendly staff to schedule your appointment"}),(0,i.jsx)("p",{className:"text-primary font-semibold",children:p.r_.phone})]})})}),(0,i.jsx)(r.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.2},viewport:{once:!0},children:(0,i.jsx)(h.Zp,{className:"hover:shadow-lg transition-shadow duration-300",children:(0,i.jsxs)(h.Wu,{className:"p-6 text-center",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,i.jsx)(l.A,{className:"h-8 w-8 text-primary"})}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Online Booking"}),(0,i.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Use our convenient online form to request an appointment"}),(0,i.jsx)(m.$,{asChild:!0,variant:"outline",size:"sm",children:(0,i.jsx)(n(),{href:"/contact",children:"Book Online"})})]})})}),(0,i.jsx)(r.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.3},viewport:{once:!0},children:(0,i.jsx)(h.Zp,{className:"hover:shadow-lg transition-shadow duration-300",children:(0,i.jsxs)(h.Wu,{className:"p-6 text-center",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,i.jsx)(c,{className:"h-8 w-8 text-primary"})}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Ask Questions"}),(0,i.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Have questions about our services? We're here to help"}),(0,i.jsx)("p",{className:"text-primary font-semibold",children:p.r_.email})]})})})]})]})})}},5525:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},6126:(e,t,a)=>{"use strict";a.d(t,{E:()=>l});var i=a(5155);a(2115);var r=a(4624),s=a(2085),n=a(9434);let o=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:a,asChild:s=!1,...l}=e,c=s?r.DX:"span";return(0,i.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(o({variant:a}),t),...l})}},6695:(e,t,a)=>{"use strict";a.d(t,{Wu:()=>l,ZB:()=>o,Zp:()=>s,aR:()=>n});var i=a(5155);a(2115);var r=a(9434);function s(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function n(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function l(e){let{className:t,...a}=e;return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}},8313:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(9946).A)("wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]])},8564:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},8673:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var i=a(5155),r=a(2605),s=a(6695),n=a(6126);function o(e){let{service:t,index:a=0,showPrice:o=!1,showFullDescription:l=!1}=e;return(0,i.jsx)(r.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1*a},children:(0,i.jsxs)(s.Zp,{className:"h-full hover:shadow-lg transition-shadow duration-300",children:[(0,i.jsxs)(s.aR,{children:[(0,i.jsx)("div",{className:"w-full h-48 bg-gray-200 rounded-lg mb-4 overflow-hidden",children:(0,i.jsx)("img",{src:t.image,alt:t.name,className:"w-full h-full object-cover",onError:e=>{e.target.src="https://images.unsplash.com/photo-1606811841689-23dfddce3e95?w=400&h=300&fit=crop&crop=center"}})}),(0,i.jsx)(s.ZB,{className:"text-xl font-bold text-gray-900",children:t.name}),o&&t.price&&(0,i.jsx)(n.E,{variant:"secondary",className:"w-fit",children:t.price})]}),(0,i.jsxs)(s.Wu,{children:[(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:t.description}),l&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Benefits:"}),(0,i.jsx)("ul",{className:"list-disc list-inside space-y-1 text-sm text-gray-600",children:t.benefits.map((e,t)=>(0,i.jsx)("li",{children:e},t))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Procedure:"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:t.procedure})]})]})]})]})})}},8720:(e,t,a)=>{"use strict";a.d(t,{$p:()=>r,FF:()=>n,rR:()=>s,r_:()=>o,x0:()=>i,x1:()=>l});let i={name:"Dental Care Clinic Biratnagar",tagline:"Your Smile, Our Priority",mission:"To provide exceptional dental care with compassion, using the latest technology and techniques to ensure every patient receives the highest quality treatment in a comfortable, welcoming environment.",story:"Established in 2015, Dental Care Clinic Biratnagar has been serving the community with dedication and excellence. Our clinic was founded with the vision of making quality dental care accessible to everyone in Biratnagar and surrounding areas.",established:"2015",location:"Biratnagar, Nepal"},r=[{id:"1",name:"Dental Cleaning & Checkups",description:"Comprehensive oral examination and professional teeth cleaning to maintain optimal oral health.",benefits:["Prevents cavities and gum disease","Early detection of dental problems","Fresh breath and clean feeling","Maintains overall oral health"],procedure:"Our dental hygienists perform thorough cleaning using ultrasonic scalers and hand instruments, followed by polishing and fluoride treatment.",image:"/images/services/cleaning.jpg",price:"NPR 1,500 - 2,500"},{id:"2",name:"Teeth Whitening",description:"Professional teeth whitening treatments to brighten your smile and boost your confidence.",benefits:["Removes stains and discoloration","Safe and effective results","Boosts self-confidence","Long-lasting whitening effect"],procedure:"We use professional-grade whitening gel applied with custom trays or in-office laser whitening for immediate results.",image:"/images/services/whitening.jpg",price:"NPR 8,000 - 15,000"},{id:"3",name:"Orthodontics",description:"Comprehensive orthodontic treatment including braces and aligners to straighten teeth and correct bite issues.",benefits:["Straighter, more attractive smile","Improved bite function","Better oral hygiene","Enhanced self-esteem"],procedure:"Treatment begins with comprehensive examination, followed by custom treatment plan using traditional braces or clear aligners.",image:"/images/services/orthodontics.jpg",price:"NPR 50,000 - 150,000"},{id:"4",name:"Dental Implants",description:"Permanent tooth replacement solution using titanium implants for missing teeth.",benefits:["Permanent tooth replacement","Natural look and feel","Preserves jawbone structure","No impact on adjacent teeth"],procedure:"Surgical placement of titanium implant into jawbone, followed by healing period and crown placement.",image:"/images/services/implants.jpg",price:"NPR 80,000 - 120,000"},{id:"5",name:"Root Canal Treatment",description:"Advanced endodontic treatment to save infected or severely damaged teeth.",benefits:["Saves natural tooth","Eliminates pain and infection","Prevents further complications","Cost-effective tooth preservation"],procedure:"Removal of infected pulp, cleaning and disinfection of root canals, followed by filling and crown placement.",image:"/images/services/root-canal.jpg",price:"NPR 8,000 - 15,000"},{id:"6",name:"Cosmetic Dentistry",description:"Aesthetic dental treatments including veneers, bonding, and smile makeovers.",benefits:["Enhanced smile appearance","Improved facial aesthetics","Boosted confidence","Customized treatment plans"],procedure:"Comprehensive smile analysis followed by customized treatment using veneers, bonding, or other cosmetic procedures.",image:"/images/services/cosmetic.jpg",price:"NPR 15,000 - 80,000"}],s=[{id:"1",name:"Sita Rai",city:"Biratnagar",rating:5,review:"Excellent service! Dr. Rajesh performed my dental implant surgery with great care. The staff is very professional and the clinic is very clean. Highly recommended!",service:"Dental Implants",date:"2024-01-15",image:"/images/testimonials/sita.jpg"},{id:"2",name:"Ramesh Gurung",city:"Dharan",rating:5,review:"I had my teeth whitening done here and the results are amazing! Dr. Priya explained everything clearly and the treatment was painless. Very satisfied with the service.",service:"Teeth Whitening",date:"2024-02-20",image:"/images/testimonials/ramesh.jpg"},{id:"3",name:"Maya Shrestha",city:"Biratnagar",rating:5,review:"The orthodontic treatment for my daughter was excellent. Dr. Priya was very patient and gentle with her. The braces were fitted perfectly and the results are wonderful.",service:"Orthodontics",date:"2024-03-10",image:"/images/testimonials/maya.jpg"},{id:"4",name:"Bikash Limbu",city:"Itahari",rating:4,review:"Great experience with root canal treatment. Dr. Amit made sure I was comfortable throughout the procedure. The clinic has modern equipment and very hygienic environment.",service:"Root Canal Treatment",date:"2024-03-25",image:"/images/testimonials/bikash.jpg"},{id:"5",name:"Sunita Karki",city:"Biratnagar",rating:5,review:"Regular dental checkups here have kept my teeth healthy. The staff is friendly and the doctors are very knowledgeable. Best dental clinic in Biratnagar!",service:"Dental Cleaning",date:"2024-04-05",image:"/images/testimonials/sunita.jpg"}],n=[{id:"1",src:"/images/gallery/reception.jpg",alt:"Modern reception area",category:"facility",title:"Reception Area",description:"Comfortable and welcoming reception area"},{id:"2",src:"/images/gallery/treatment-room.jpg",alt:"Treatment room with modern equipment",category:"facility",title:"Treatment Room",description:"State-of-the-art treatment rooms"},{id:"3",src:"/images/gallery/dental-chair.jpg",alt:"Modern dental chair",category:"equipment",title:"Dental Chair",description:"Comfortable modern dental chairs"},{id:"4",src:"/images/gallery/xray-machine.jpg",alt:"Digital X-ray machine",category:"equipment",title:"Digital X-Ray",description:"Advanced digital X-ray technology"},{id:"5",src:"/images/gallery/sterilization.jpg",alt:"Sterilization equipment",category:"equipment",title:"Sterilization Unit",description:"Advanced sterilization equipment"},{id:"6",src:"/images/gallery/team-photo.jpg",alt:"Dental team photo",category:"team",title:"Our Team",description:"Professional dental care team"}],o={address:"Main Road, Biratnagar-10, Morang, Nepal",phone:"+977-21-525678",email:"<EMAIL>",emergencyPhone:"+977-9841234567",businessHours:{Sunday:"10:00 AM - 6:00 PM",Monday:"10:00 AM - 6:00 PM",Tuesday:"10:00 AM - 6:00 PM",Wednesday:"10:00 AM - 6:00 PM",Thursday:"10:00 AM - 6:00 PM",Friday:"10:00 AM - 6:00 PM",Saturday:"Closed"},socialMedia:{facebook:"https://facebook.com/dentalcarebiratnagar",instagram:"https://instagram.com/dentalcarebiratnagar",twitter:"https://twitter.com/dentalcarebiratnagar"},mapEmbedUrl:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3571.5234567890123!2d87.2734567890123!3d26.4567890123456!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjbCsDI3JzI0LjQiTiA4N8KwMTYnMjQuNCJF!5e0!3m2!1sen!2snp!4v1234567890123!5m2!1sen!2snp"},l=[{id:"1",name:"Dr. Rajesh Sharma",role:"Chief Dentist & Founder",qualification:"BDS, MDS (Oral & Maxillofacial Surgery)",experience:"12+ years",bio:"Dr. Rajesh Sharma is a highly experienced oral and maxillofacial surgeon with over 12 years of practice. He founded Dental Care Clinic Biratnagar with the vision of providing world-class dental care to the community.",image:"/images/team/dr-rajesh.jpg",specialties:["Oral Surgery","Dental Implants","Complex Extractions","Facial Trauma"]},{id:"2",name:"Dr. Priya Adhikari",role:"Orthodontist",qualification:"BDS, MDS (Orthodontics)",experience:"8+ years",bio:"Dr. Priya Adhikari specializes in orthodontics and has helped hundreds of patients achieve beautiful, straight smiles. She is known for her gentle approach and attention to detail.",image:"/images/team/dr-priya.jpg",specialties:["Braces","Clear Aligners","Pediatric Orthodontics","Bite Correction"]},{id:"3",name:"Dr. Amit Thapa",role:"Endodontist",qualification:"BDS, MDS (Endodontics)",experience:"6+ years",bio:"Dr. Amit Thapa is our endodontic specialist, focusing on root canal treatments and saving natural teeth. His expertise in pain management ensures comfortable treatment experiences.",image:"/images/team/dr-amit.jpg",specialties:["Root Canal Treatment","Endodontic Surgery","Pain Management","Dental Trauma"]}]},9074:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9420:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>s});var i=a(2596),r=a(9688);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,i.$)(t))}}},e=>{e.O(0,[939,874,441,964,358],()=>e(e.s=1703)),_N_E=e.O()}]);