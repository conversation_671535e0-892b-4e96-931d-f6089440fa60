exports.id=753,exports.ids=[753],exports.modules={4780:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}},15736:(a,b,c)=>{"use strict";c.d(b,{default:()=>p});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(51743),i=c(48340),j=c(97992),k=c(11860),l=c(12941),m=c(29523),n=c(83446);let o=[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Services",href:"/services"},{name:"Gallery",href:"/gallery"},{name:"Testimonials",href:"/testimonials"},{name:"Contact",href:"/contact"}];function p(){let[a,b]=(0,e.useState)(!1);return(0,d.jsxs)("header",{className:"bg-white shadow-sm sticky top-0 z-50",children:[(0,d.jsx)("div",{className:"bg-primary text-primary-foreground py-2",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(i.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:n.r_.phone})]}),(0,d.jsxs)("div",{className:"hidden sm:flex items-center space-x-1",children:[(0,d.jsx)(j.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:n.r_.address})]})]}),(0,d.jsx)("div",{className:"hidden md:block",children:(0,d.jsxs)("span",{children:["Emergency: ",n.r_.emergencyPhone]})})]})})}),(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsxs)(g(),{href:"/",className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-primary rounded-full flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-primary-foreground font-bold text-lg",children:"DC"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:n.x0.name}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:n.x0.tagline})]})]})}),(0,d.jsx)("nav",{className:"hidden md:flex space-x-8",children:o.map(a=>(0,d.jsx)(g(),{href:a.href,className:"text-gray-700 hover:text-primary transition-colors duration-200 font-medium",children:a.name},a.name))}),(0,d.jsx)("div",{className:"hidden md:block",children:(0,d.jsx)(m.$,{asChild:!0,children:(0,d.jsx)(g(),{href:"/contact",children:"Book Appointment"})})}),(0,d.jsx)("div",{className:"md:hidden",children:(0,d.jsx)(m.$,{variant:"ghost",size:"sm",onClick:()=>b(!a),children:a?(0,d.jsx)(k.A,{className:"h-6 w-6"}):(0,d.jsx)(l.A,{className:"h-6 w-6"})})})]})}),a&&(0,d.jsx)(h.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"md:hidden bg-white border-t",children:(0,d.jsxs)("div",{className:"px-4 py-2 space-y-1",children:[o.map(a=>(0,d.jsx)(g(),{href:a.href,className:"block px-3 py-2 text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md transition-colors duration-200",onClick:()=>b(!1),children:a.name},a.name)),(0,d.jsx)("div",{className:"pt-2",children:(0,d.jsx)(m.$,{asChild:!0,className:"w-full",children:(0,d.jsx)(g(),{href:"/contact",onClick:()=>b(!1),children:"Book Appointment"})})})]})})]})}},23862:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},26203:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23)),Promise.resolve().then(c.bind(c,15736))},29523:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(60687);c(43210);var e=c(81391),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},60547:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23)),Promise.resolve().then(c.bind(c,68926))},60814:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},61135:()=>{},68926:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\dental\\\\dental-clinic\\\\src\\\\components\\\\layout\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\components\\layout\\Header.tsx","default")},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},83446:(a,b,c)=>{"use strict";c.d(b,{$p:()=>e,FF:()=>g,rR:()=>f,r_:()=>h,x0:()=>d,x1:()=>i});let d={name:"Dental Care Clinic Biratnagar",tagline:"Your Smile, Our Priority",mission:"To provide exceptional dental care with compassion, using the latest technology and techniques to ensure every patient receives the highest quality treatment in a comfortable, welcoming environment.",story:"Established in 2015, Dental Care Clinic Biratnagar has been serving the community with dedication and excellence. Our clinic was founded with the vision of making quality dental care accessible to everyone in Biratnagar and surrounding areas.",established:"2015",location:"Biratnagar, Nepal"},e=[{id:"1",name:"Dental Cleaning & Checkups",description:"Comprehensive oral examination and professional teeth cleaning to maintain optimal oral health.",benefits:["Prevents cavities and gum disease","Early detection of dental problems","Fresh breath and clean feeling","Maintains overall oral health"],procedure:"Our dental hygienists perform thorough cleaning using ultrasonic scalers and hand instruments, followed by polishing and fluoride treatment.",image:"/images/services/cleaning.jpg",price:"NPR 1,500 - 2,500"},{id:"2",name:"Teeth Whitening",description:"Professional teeth whitening treatments to brighten your smile and boost your confidence.",benefits:["Removes stains and discoloration","Safe and effective results","Boosts self-confidence","Long-lasting whitening effect"],procedure:"We use professional-grade whitening gel applied with custom trays or in-office laser whitening for immediate results.",image:"/images/services/whitening.jpg",price:"NPR 8,000 - 15,000"},{id:"3",name:"Orthodontics",description:"Comprehensive orthodontic treatment including braces and aligners to straighten teeth and correct bite issues.",benefits:["Straighter, more attractive smile","Improved bite function","Better oral hygiene","Enhanced self-esteem"],procedure:"Treatment begins with comprehensive examination, followed by custom treatment plan using traditional braces or clear aligners.",image:"/images/services/orthodontics.jpg",price:"NPR 50,000 - 150,000"},{id:"4",name:"Dental Implants",description:"Permanent tooth replacement solution using titanium implants for missing teeth.",benefits:["Permanent tooth replacement","Natural look and feel","Preserves jawbone structure","No impact on adjacent teeth"],procedure:"Surgical placement of titanium implant into jawbone, followed by healing period and crown placement.",image:"/images/services/implants.jpg",price:"NPR 80,000 - 120,000"},{id:"5",name:"Root Canal Treatment",description:"Advanced endodontic treatment to save infected or severely damaged teeth.",benefits:["Saves natural tooth","Eliminates pain and infection","Prevents further complications","Cost-effective tooth preservation"],procedure:"Removal of infected pulp, cleaning and disinfection of root canals, followed by filling and crown placement.",image:"/images/services/root-canal.jpg",price:"NPR 8,000 - 15,000"},{id:"6",name:"Cosmetic Dentistry",description:"Aesthetic dental treatments including veneers, bonding, and smile makeovers.",benefits:["Enhanced smile appearance","Improved facial aesthetics","Boosted confidence","Customized treatment plans"],procedure:"Comprehensive smile analysis followed by customized treatment using veneers, bonding, or other cosmetic procedures.",image:"/images/services/cosmetic.jpg",price:"NPR 15,000 - 80,000"}],f=[{id:"1",name:"Sita Rai",city:"Biratnagar",rating:5,review:"Excellent service! Dr. Rajesh performed my dental implant surgery with great care. The staff is very professional and the clinic is very clean. Highly recommended!",service:"Dental Implants",date:"2024-01-15",image:"/images/testimonials/sita.jpg"},{id:"2",name:"Ramesh Gurung",city:"Dharan",rating:5,review:"I had my teeth whitening done here and the results are amazing! Dr. Priya explained everything clearly and the treatment was painless. Very satisfied with the service.",service:"Teeth Whitening",date:"2024-02-20",image:"/images/testimonials/ramesh.jpg"},{id:"3",name:"Maya Shrestha",city:"Biratnagar",rating:5,review:"The orthodontic treatment for my daughter was excellent. Dr. Priya was very patient and gentle with her. The braces were fitted perfectly and the results are wonderful.",service:"Orthodontics",date:"2024-03-10",image:"/images/testimonials/maya.jpg"},{id:"4",name:"Bikash Limbu",city:"Itahari",rating:4,review:"Great experience with root canal treatment. Dr. Amit made sure I was comfortable throughout the procedure. The clinic has modern equipment and very hygienic environment.",service:"Root Canal Treatment",date:"2024-03-25",image:"/images/testimonials/bikash.jpg"},{id:"5",name:"Sunita Karki",city:"Biratnagar",rating:5,review:"Regular dental checkups here have kept my teeth healthy. The staff is friendly and the doctors are very knowledgeable. Best dental clinic in Biratnagar!",service:"Dental Cleaning",date:"2024-04-05",image:"/images/testimonials/sunita.jpg"}],g=[{id:"1",src:"/images/gallery/reception.jpg",alt:"Modern reception area",category:"facility",title:"Reception Area",description:"Comfortable and welcoming reception area"},{id:"2",src:"/images/gallery/treatment-room.jpg",alt:"Treatment room with modern equipment",category:"facility",title:"Treatment Room",description:"State-of-the-art treatment rooms"},{id:"3",src:"/images/gallery/dental-chair.jpg",alt:"Modern dental chair",category:"equipment",title:"Dental Chair",description:"Comfortable modern dental chairs"},{id:"4",src:"/images/gallery/xray-machine.jpg",alt:"Digital X-ray machine",category:"equipment",title:"Digital X-Ray",description:"Advanced digital X-ray technology"},{id:"5",src:"/images/gallery/sterilization.jpg",alt:"Sterilization equipment",category:"equipment",title:"Sterilization Unit",description:"Advanced sterilization equipment"},{id:"6",src:"/images/gallery/team-photo.jpg",alt:"Dental team photo",category:"team",title:"Our Team",description:"Professional dental care team"}],h={address:"Main Road, Biratnagar-10, Morang, Nepal",phone:"+977-21-525678",email:"<EMAIL>",emergencyPhone:"+977-9841234567",businessHours:{Sunday:"10:00 AM - 6:00 PM",Monday:"10:00 AM - 6:00 PM",Tuesday:"10:00 AM - 6:00 PM",Wednesday:"10:00 AM - 6:00 PM",Thursday:"10:00 AM - 6:00 PM",Friday:"10:00 AM - 6:00 PM",Saturday:"Closed"},socialMedia:{facebook:"https://facebook.com/dentalcarebiratnagar",instagram:"https://instagram.com/dentalcarebiratnagar",twitter:"https://twitter.com/dentalcarebiratnagar"},mapEmbedUrl:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3571.5234567890123!2d87.2734567890123!3d26.4567890123456!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjbCsDI3JzI0LjQiTiA4N8KwMTYnMjQuNCJF!5e0!3m2!1sen!2snp!4v1234567890123!5m2!1sen!2snp"},i=[{id:"1",name:"Dr. Rajesh Sharma",role:"Chief Dentist & Founder",qualification:"BDS, MDS (Oral & Maxillofacial Surgery)",experience:"12+ years",bio:"Dr. Rajesh Sharma is a highly experienced oral and maxillofacial surgeon with over 12 years of practice. He founded Dental Care Clinic Biratnagar with the vision of providing world-class dental care to the community.",image:"/images/team/dr-rajesh.jpg",specialties:["Oral Surgery","Dental Implants","Complex Extractions","Facial Trauma"]},{id:"2",name:"Dr. Priya Adhikari",role:"Orthodontist",qualification:"BDS, MDS (Orthodontics)",experience:"8+ years",bio:"Dr. Priya Adhikari specializes in orthodontics and has helped hundreds of patients achieve beautiful, straight smiles. She is known for her gentle approach and attention to detail.",image:"/images/team/dr-priya.jpg",specialties:["Braces","Clear Aligners","Pediatric Orthodontics","Bite Correction"]},{id:"3",name:"Dr. Amit Thapa",role:"Endodontist",qualification:"BDS, MDS (Endodontics)",experience:"6+ years",bio:"Dr. Amit Thapa is our endodontic specialist, focusing on root canal treatments and saving natural teeth. His expertise in pain management ensures comfortable treatment experiences.",image:"/images/team/dr-amit.jpg",specialties:["Root Canal Treatment","Endodontic Surgery","Pain Management","Dental Trauma"]}]},92302:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>y,metadata:()=>x});var d=c(37413),e=c(35293),f=c.n(e);c(61135);var g=c(68926),h=c(4536),i=c.n(h),j=c(45868),k=c(56378),l=c(63353),m=c(49046),n=c(71750),o=c(60343),p=c(53148);let q={name:"Dental Care Clinic Biratnagar",mission:"To provide exceptional dental care with compassion, using the latest technology and techniques to ensure every patient receives the highest quality treatment in a comfortable, welcoming environment."},r=[{id:"1",name:"Dental Cleaning & Checkups",description:"Comprehensive oral examination and professional teeth cleaning to maintain optimal oral health.",benefits:["Prevents cavities and gum disease","Early detection of dental problems","Fresh breath and clean feeling","Maintains overall oral health"],procedure:"Our dental hygienists perform thorough cleaning using ultrasonic scalers and hand instruments, followed by polishing and fluoride treatment.",image:"/images/services/cleaning.jpg",price:"NPR 1,500 - 2,500"},{id:"2",name:"Teeth Whitening",description:"Professional teeth whitening treatments to brighten your smile and boost your confidence.",benefits:["Removes stains and discoloration","Safe and effective results","Boosts self-confidence","Long-lasting whitening effect"],procedure:"We use professional-grade whitening gel applied with custom trays or in-office laser whitening for immediate results.",image:"/images/services/whitening.jpg",price:"NPR 8,000 - 15,000"},{id:"3",name:"Orthodontics",description:"Comprehensive orthodontic treatment including braces and aligners to straighten teeth and correct bite issues.",benefits:["Straighter, more attractive smile","Improved bite function","Better oral hygiene","Enhanced self-esteem"],procedure:"Treatment begins with comprehensive examination, followed by custom treatment plan using traditional braces or clear aligners.",image:"/images/services/orthodontics.jpg",price:"NPR 50,000 - 150,000"},{id:"4",name:"Dental Implants",description:"Permanent tooth replacement solution using titanium implants for missing teeth.",benefits:["Permanent tooth replacement","Natural look and feel","Preserves jawbone structure","No impact on adjacent teeth"],procedure:"Surgical placement of titanium implant into jawbone, followed by healing period and crown placement.",image:"/images/services/implants.jpg",price:"NPR 80,000 - 120,000"},{id:"5",name:"Root Canal Treatment",description:"Advanced endodontic treatment to save infected or severely damaged teeth.",benefits:["Saves natural tooth","Eliminates pain and infection","Prevents further complications","Cost-effective tooth preservation"],procedure:"Removal of infected pulp, cleaning and disinfection of root canals, followed by filling and crown placement.",image:"/images/services/root-canal.jpg",price:"NPR 8,000 - 15,000"},{id:"6",name:"Cosmetic Dentistry",description:"Aesthetic dental treatments including veneers, bonding, and smile makeovers.",benefits:["Enhanced smile appearance","Improved facial aesthetics","Boosted confidence","Customized treatment plans"],procedure:"Comprehensive smile analysis followed by customized treatment using veneers, bonding, or other cosmetic procedures.",image:"/images/services/cosmetic.jpg",price:"NPR 15,000 - 80,000"}],s={address:"Main Road, Biratnagar-10, Morang, Nepal",phone:"+977-21-525678",email:"<EMAIL>",socialMedia:{facebook:"https://facebook.com/dentalcarebiratnagar",instagram:"https://instagram.com/dentalcarebiratnagar",twitter:"https://twitter.com/dentalcarebiratnagar"}},t=[{id:"1",name:"Dr. Rajesh Sharma",role:"Chief Dentist & Founder",qualification:"BDS, MDS (Oral & Maxillofacial Surgery)",experience:"12+ years",bio:"Dr. Rajesh Sharma is a highly experienced oral and maxillofacial surgeon with over 12 years of practice. He founded Dental Care Clinic Biratnagar with the vision of providing world-class dental care to the community.",image:"/images/team/dr-rajesh.jpg",specialties:["Oral Surgery","Dental Implants","Complex Extractions","Facial Trauma"]},{id:"2",name:"Dr. Priya Adhikari",role:"Orthodontist",qualification:"BDS, MDS (Orthodontics)",experience:"8+ years",bio:"Dr. Priya Adhikari specializes in orthodontics and has helped hundreds of patients achieve beautiful, straight smiles. She is known for her gentle approach and attention to detail.",image:"/images/team/dr-priya.jpg",specialties:["Braces","Clear Aligners","Pediatric Orthodontics","Bite Correction"]},{id:"3",name:"Dr. Amit Thapa",role:"Endodontist",qualification:"BDS, MDS (Endodontics)",experience:"6+ years",bio:"Dr. Amit Thapa is our endodontic specialist, focusing on root canal treatments and saving natural teeth. His expertise in pain management ensures comfortable treatment experiences.",image:"/images/team/dr-amit.jpg",specialties:["Root Canal Treatment","Endodontic Surgery","Pain Management","Dental Trauma"]}];function u(){return(0,d.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-primary rounded-full flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-primary-foreground font-bold",children:"DC"})}),(0,d.jsx)("h3",{className:"text-lg font-bold",children:q.name})]}),(0,d.jsx)("p",{className:"text-gray-300 text-sm leading-relaxed",children:q.mission}),(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsx)(i(),{href:s.socialMedia.facebook||"#",className:"text-gray-400 hover:text-white transition-colors","aria-label":"Facebook",children:(0,d.jsx)(j.A,{className:"h-5 w-5"})}),(0,d.jsx)(i(),{href:s.socialMedia.instagram||"#",className:"text-gray-400 hover:text-white transition-colors","aria-label":"Instagram",children:(0,d.jsx)(k.A,{className:"h-5 w-5"})}),(0,d.jsx)(i(),{href:s.socialMedia.twitter||"#",className:"text-gray-400 hover:text-white transition-colors","aria-label":"Twitter",children:(0,d.jsx)(l.A,{className:"h-5 w-5"})})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"Quick Links"}),(0,d.jsxs)("ul",{className:"space-y-2",children:[(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/about",className:"text-gray-300 hover:text-white transition-colors",children:"About Us"})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/services",className:"text-gray-300 hover:text-white transition-colors",children:"Our Services"})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/gallery",className:"text-gray-300 hover:text-white transition-colors",children:"Gallery"})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/testimonials",className:"text-gray-300 hover:text-white transition-colors",children:"Testimonials"})}),(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:"/contact",className:"text-gray-300 hover:text-white transition-colors",children:"Contact Us"})})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"Our Services"}),(0,d.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,d.jsx)("li",{className:"text-gray-300",children:"Dental Cleaning & Checkups"}),(0,d.jsx)("li",{className:"text-gray-300",children:"Teeth Whitening"}),(0,d.jsx)("li",{className:"text-gray-300",children:"Orthodontics"}),(0,d.jsx)("li",{className:"text-gray-300",children:"Dental Implants"}),(0,d.jsx)("li",{className:"text-gray-300",children:"Root Canal Treatment"}),(0,d.jsx)("li",{className:"text-gray-300",children:"Cosmetic Dentistry"})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"Contact Info"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)(m.A,{className:"h-5 w-5 text-primary mt-0.5 flex-shrink-0"}),(0,d.jsx)("p",{className:"text-gray-300 text-sm",children:s.address})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(n.A,{className:"h-5 w-5 text-primary flex-shrink-0"}),(0,d.jsx)("p",{className:"text-gray-300 text-sm",children:s.phone})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(o.A,{className:"h-5 w-5 text-primary flex-shrink-0"}),(0,d.jsx)("p",{className:"text-gray-300 text-sm",children:s.email})]}),(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)(p.A,{className:"h-5 w-5 text-primary mt-0.5 flex-shrink-0"}),(0,d.jsxs)("div",{className:"text-gray-300 text-sm",children:[(0,d.jsx)("p",{children:"Mon-Fri: 10:00 AM - 6:00 PM"}),(0,d.jsx)("p",{children:"Saturday: Closed"}),(0,d.jsx)("p",{children:"Sunday: 10:00 AM - 6:00 PM"})]})]})]})]})]}),(0,d.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,d.jsxs)("p",{className:"text-gray-400 text-sm",children:["\xa9 ",new Date().getFullYear()," ",q.name,". All rights reserved."]}),(0,d.jsxs)("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[(0,d.jsx)(i(),{href:"#",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Privacy Policy"}),(0,d.jsx)(i(),{href:"#",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Terms of Service"})]})]})})]})})}function v(){let a={"@context":"https://schema.org","@type":"Dentist",name:q.name,description:q.mission,url:"https://dentalcarebiratnagar.com",telephone:s.phone,email:s.email,address:{"@type":"PostalAddress",streetAddress:s.address,addressLocality:"Biratnagar",addressRegion:"Morang",addressCountry:"Nepal"},geo:{"@type":"GeoCoordinates",latitude:"26.4567890123456",longitude:"87.2734567890123"},openingHours:["Mo-Fr 10:00-18:00","Su 10:00-18:00"],priceRange:"$$",image:"https://dentalcarebiratnagar.com/images/clinic-exterior.jpg",sameAs:[s.socialMedia.facebook,s.socialMedia.instagram,s.socialMedia.twitter],hasOfferCatalog:{"@type":"OfferCatalog",name:"Dental Services",itemListElement:r.map(a=>({"@type":"Offer",itemOffered:{"@type":"Service",name:a.name,description:a.description}}))},employee:t.map(a=>({"@type":"Person",name:a.name,jobTitle:a.role,description:a.bio})),aggregateRating:{"@type":"AggregateRating",ratingValue:"4.9",reviewCount:"500",bestRating:"5",worstRating:"1"}};return(0,d.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(a)}})}function w(){let a={"@context":"https://schema.org","@type":"MedicalOrganization",name:q.name,description:q.mission,url:"https://dentalcarebiratnagar.com",telephone:s.phone,email:s.email,address:{"@type":"PostalAddress",streetAddress:s.address,addressLocality:"Biratnagar",addressRegion:"Morang",addressCountry:"Nepal"},medicalSpecialty:["Dentistry","Orthodontics","Endodontics","Oral Surgery","Cosmetic Dentistry"],availableService:r.map(a=>({"@type":"MedicalProcedure",name:a.name,description:a.description}))};return(0,d.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(a)}})}let x={title:"Dental Care Clinic Biratnagar | Best Dentist in Biratnagar, Nepal",description:"Professional dental care services in Biratnagar, Nepal. Expert dentists providing teeth cleaning, whitening, orthodontics, implants and comprehensive oral health care.",keywords:"dental clinic Biratnagar, best dentist Biratnagar, teeth whitening Biratnagar, dental care Nepal, orthodontics Biratnagar, dental implants",authors:[{name:"Dental Care Clinic Biratnagar"}],openGraph:{title:"Dental Care Clinic Biratnagar | Best Dentist in Biratnagar, Nepal",description:"Professional dental care services in Biratnagar, Nepal. Expert dentists providing comprehensive oral health care.",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"Dental Care Clinic Biratnagar | Best Dentist in Biratnagar, Nepal",description:"Professional dental care services in Biratnagar, Nepal."},robots:{index:!0,follow:!0}};function y({children:a}){return(0,d.jsxs)("html",{lang:"en",children:[(0,d.jsxs)("head",{children:[(0,d.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,d.jsx)("meta",{name:"theme-color",content:"#3b82f6"}),(0,d.jsx)(v,{}),(0,d.jsx)(w,{})]}),(0,d.jsxs)("body",{className:`${f().variable} font-poppins antialiased bg-white text-gray-900`,children:[(0,d.jsx)(g.default,{}),(0,d.jsx)("main",{className:"min-h-screen",children:a}),(0,d.jsx)(u,{})]})]})}}};