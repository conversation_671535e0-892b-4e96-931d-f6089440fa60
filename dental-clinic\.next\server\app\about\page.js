(()=>{var a={};a.id=220,a.ids=[220],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22900:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\dental\\\\dental-clinic\\\\src\\\\components\\\\sections\\\\AboutHero.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\components\\sections\\AboutHero.tsx","default")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},28770:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(37413),e=c(22900),f=c(78028),g=c(43195),h=c(86824);let i={title:"About Us | Dental Care Clinic Biratnagar - Expert Dental Team",description:"Learn about our experienced dental team, clinic story, and commitment to serving the Biratnagar community with exceptional dental care since 2015.",keywords:"about dental clinic Biratnagar, dental team Biratnagar, dentist experience, dental care history"};function j(){return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(e.default,{}),(0,d.jsx)(f.default,{}),(0,d.jsx)(g.default,{}),(0,d.jsx)(h.default,{})]})}},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30926:(a,b,c)=>{"use strict";c.d(b,{default:()=>h});var d=c(60687),e=c(51743),f=c(71134),g=c(83446);function h(){return(0,d.jsx)(f.w,{background:"gray",className:"pt-32 pb-20",children:(0,d.jsxs)("div",{className:"text-center max-w-4xl mx-auto",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,d.jsx)("p",{className:"text-primary font-semibold text-sm uppercase tracking-wide mb-4",children:"About Our Clinic"}),(0,d.jsxs)("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6",children:["Caring for Smiles Since"," ",(0,d.jsx)("span",{className:"text-primary",children:g.x0.established})]}),(0,d.jsx)("p",{className:"text-xl text-gray-600 leading-relaxed mb-8",children:g.x0.mission})]}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"relative w-full h-96 rounded-2xl overflow-hidden shadow-2xl",children:[(0,d.jsx)("img",{src:"https://images.unsplash.com/photo-**********-2a8555f1a136?w=800&h=400&fit=crop&crop=center",alt:"Our dental clinic team",className:"w-full h-full object-cover"}),(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"}),(0,d.jsxs)("div",{className:"absolute bottom-6 left-6 text-white",children:[(0,d.jsx)("p",{className:"text-lg font-semibold",children:"Our Professional Team"}),(0,d.jsx)("p",{className:"text-sm opacity-90",children:"Dedicated to your oral health"})]})]})]})})}},33873:a=>{"use strict";a.exports=require("path")},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41312:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},43195:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\dental\\\\dental-clinic\\\\src\\\\components\\\\sections\\\\TeamSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\components\\sections\\TeamSection.tsx","default")},44493:(a,b,c)=>{"use strict";c.d(b,{Wu:()=>i,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},62038:(a,b,c)=>{"use strict";c.d(b,{default:()=>m});var d=c(60687),e=c(51743),f=c(40228),g=c(41312),h=c(86561),i=c(97992),j=c(71134),k=c(83446);let l=[{year:"2015",title:"Clinic Founded",description:"Dr. Rajesh Sharma established Dental Care Clinic Biratnagar with a vision to provide world-class dental care to the local community.",icon:f.A},{year:"2017",title:"Team Expansion",description:"Added specialized dentists including orthodontist Dr. Priya Adhikari and endodontist Dr. Amit Thapa to our growing team.",icon:g.A},{year:"2019",title:"Modern Equipment",description:"Invested in state-of-the-art dental equipment including digital X-ray machines and advanced sterilization systems.",icon:h.A},{year:"2024",title:"Community Leader",description:"Recognized as the leading dental clinic in Biratnagar, having served over 5,000 patients with exceptional care.",icon:i.A}];function m(){return(0,d.jsx)(j.w,{background:"white",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center",children:[(0,d.jsxs)(e.P.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-8",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-primary font-semibold text-sm uppercase tracking-wide mb-4",children:"Our Story"}),(0,d.jsxs)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-6",children:["A Journey of"," ",(0,d.jsx)("span",{className:"text-primary",children:"Excellence"})]}),(0,d.jsxs)("div",{className:"space-y-4 text-gray-600 leading-relaxed",children:[(0,d.jsx)("p",{children:k.x0.story}),(0,d.jsx)("p",{children:"What started as a small practice has grown into Biratnagar's most trusted dental clinic. Our commitment to excellence, combined with the latest dental technology and a compassionate approach, has made us the preferred choice for families throughout the region."}),(0,d.jsx)("p",{children:"We believe that everyone deserves access to quality dental care, which is why we've made it our mission to provide comprehensive services at affordable prices, without compromising on quality or comfort."})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"bg-primary/5 p-6 rounded-xl",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Our Mission"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"To provide exceptional dental care with compassion and integrity."})]}),(0,d.jsxs)("div",{className:"bg-accent/5 p-6 rounded-xl",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Our Vision"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"To be the leading dental clinic in Nepal, setting standards for quality care."})]})]})]}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-8",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Our Journey"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Key milestones in our clinic's history"})]}),(0,d.jsx)("div",{className:"space-y-6",children:l.map((a,b)=>(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},viewport:{once:!0},className:"flex items-start space-x-4",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("div",{className:"w-12 h-12 bg-primary rounded-full flex items-center justify-center",children:(0,d.jsx)(a.icon,{className:"h-6 w-6 text-white"})})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,d.jsx)("span",{className:"text-lg font-bold text-primary",children:a.year}),(0,d.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:a.title})]}),(0,d.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:a.description})]})]},a.year))})]})]})})}},62512:(a,b,c)=>{"use strict";c.d(b,{default:()=>l});var d=c(60687),e=c(51743);let f=(0,c(62688).A)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]);var g=c(48730),h=c(71134),i=c(44493),j=c(96834),k=c(83446);function l(){return(0,d.jsxs)(h.w,{background:"gray",children:[(0,d.jsx)(h.X,{subtitle:"Meet Our Team",title:"Expert Dental Professionals",description:"Our experienced team of dental professionals is dedicated to providing you with the highest quality care in a comfortable and welcoming environment."}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:k.x1.map((a,b)=>(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},viewport:{once:!0},children:(0,d.jsx)(i.Zp,{className:"h-full hover:shadow-lg transition-shadow duration-300",children:(0,d.jsxs)(i.Wu,{className:"p-6",children:[(0,d.jsx)("div",{className:"w-full h-64 bg-gray-200 rounded-xl mb-6 overflow-hidden",children:(0,d.jsx)("img",{src:a.image,alt:a.name,className:"w-full h-full object-cover",onError:a=>{a.target.src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=400&h=400&fit=crop&crop=face"}})}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-1",children:a.name}),(0,d.jsx)("p",{className:"text-primary font-semibold mb-2",children:a.role})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,d.jsx)(f,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:a.qualification})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,d.jsx)(g.A,{className:"h-4 w-4"}),(0,d.jsxs)("span",{children:[a.experience," of experience"]})]}),(0,d.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:a.bio}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-2 text-sm",children:"Specialties:"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:a.specialties.map((a,b)=>(0,d.jsx)(j.E,{variant:"secondary",className:"text-xs",children:a},b))})]})]})]})})},a.id))}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"mt-16",children:(0,d.jsx)("div",{className:"bg-white rounded-2xl p-8 shadow-sm border border-gray-100",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8 text-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:"3"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Specialist Doctors"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:"25+"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Years Combined Experience"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:"5000+"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Patients Treated"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-primary mb-2",children:"98%"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Patient Satisfaction"})]})]})})})]})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66136:(a,b,c)=>{Promise.resolve().then(c.bind(c,22900)),Promise.resolve().then(c.bind(c,78028)),Promise.resolve().then(c.bind(c,86824)),Promise.resolve().then(c.bind(c,43195))},67760:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},70334:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},71134:(a,b,c)=>{"use strict";c.d(b,{X:()=>g,w:()=>f});var d=c(60687),e=c(4780);function f({children:a,className:b,id:c,background:f="white"}){return(0,d.jsx)("section",{id:c,className:(0,e.cn)("py-16 sm:py-20",{white:"bg-white",gray:"bg-gray-50",primary:"bg-primary text-primary-foreground"}[f],b),children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a})})}function g({title:a,subtitle:b,description:c,centered:f=!0,className:g}){return(0,d.jsxs)("div",{className:(0,e.cn)("mb-12",f&&"text-center",g),children:[b&&(0,d.jsx)("p",{className:"text-primary font-semibold text-sm uppercase tracking-wide mb-2",children:b}),(0,d.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:a}),c&&(0,d.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:c})]})}},75864:(a,b,c)=>{Promise.resolve().then(c.bind(c,30926)),Promise.resolve().then(c.bind(c,62038)),Promise.resolve().then(c.bind(c,93186)),Promise.resolve().then(c.bind(c,62512))},78028:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\dental\\\\dental-clinic\\\\src\\\\components\\\\sections\\\\ClinicStory.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\components\\sections\\ClinicStory.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86561:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},86824:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\dental\\\\dental-clinic\\\\src\\\\components\\\\sections\\\\CommunityTrust.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\components\\sections\\CommunityTrust.tsx","default")},93186:(a,b,c)=>{"use strict";c.d(b,{default:()=>r});var d=c(60687),e=c(51743),f=c(85814),g=c.n(f),h=c(67760),i=c(41312),j=c(99891),k=c(86561),l=c(70334),m=c(71134),n=c(29523),o=c(44493);let p=[{icon:h.A,title:"Community Care",description:"We are deeply committed to the health and wellbeing of the Biratnagar community, providing accessible dental care for all."},{icon:i.A,title:"Local Trust",description:"Built strong relationships with local families over the years, becoming their trusted partner in oral health."},{icon:j.A,title:"Quality Assurance",description:"Maintaining the highest standards of care and safety, ensuring every patient receives exceptional treatment."},{icon:k.A,title:"Professional Excellence",description:"Recognized for our commitment to continuous learning and adopting the latest dental technologies and techniques."}],q=[{title:"Free Dental Camps",description:"Regular free dental checkup camps in schools and community centers",impact:"500+ people served annually"},{title:"Oral Health Education",description:"Educational programs about dental hygiene in local schools",impact:"20+ schools reached"},{title:"Emergency Care",description:"24/7 emergency dental services for urgent cases",impact:"Always available for emergencies"}];function r(){return(0,d.jsxs)(m.w,{background:"white",children:[(0,d.jsx)(m.X,{subtitle:"Community Impact",title:"Trusted by Biratnagar",description:"We take pride in being an integral part of the Biratnagar community, contributing to the oral health and wellbeing of our neighbors."}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16",children:p.map((a,b)=>(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},viewport:{once:!0},children:(0,d.jsx)(o.Zp,{className:"h-full text-center hover:shadow-lg transition-shadow duration-300",children:(0,d.jsxs)(o.Wu,{className:"p-6",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(a.icon,{className:"h-8 w-8 text-primary"})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:a.title}),(0,d.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:a.description})]})})},a.title))}),(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"bg-gradient-to-r from-primary/5 to-accent/5 rounded-2xl p-8 border border-primary/10 mb-16",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Our Community Initiatives"}),(0,d.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Beyond providing excellent dental care, we actively contribute to the health and education of our community through various initiatives."})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:q.map((a,b)=>(0,d.jsxs)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1*b},viewport:{once:!0},className:"bg-white rounded-xl p-6 shadow-sm",children:[(0,d.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-3",children:a.title}),(0,d.jsx)("p",{className:"text-gray-600 text-sm mb-4 leading-relaxed",children:a.description}),(0,d.jsx)("div",{className:"text-primary font-semibold text-sm",children:a.impact})]},a.title))})]}),(0,d.jsx)(e.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center",children:(0,d.jsxs)("div",{className:"bg-gray-50 rounded-2xl p-8",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Join Our Dental Family"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl mx-auto",children:"Experience the care and trust that thousands of Biratnagar families have come to rely on. Schedule your appointment today and become part of our dental family."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(n.$,{asChild:!0,size:"lg",children:(0,d.jsx)(g(),{href:"/contact",children:"Schedule Appointment"})}),(0,d.jsx)(n.$,{asChild:!0,variant:"outline",size:"lg",children:(0,d.jsxs)(g(),{href:"/services",children:["Explore Our Services",(0,d.jsx)(l.A,{className:"ml-2 h-4 w-4"})]})})]})]})})]})}},96666:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,28770)),"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\app\\about\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,92302)),"C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\ashish\\dental\\dental-clinic\\src\\app\\about\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/about/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},96834:(a,b,c)=>{"use strict";c.d(b,{E:()=>i});var d=c(60687);c(43210);var e=c(81391),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:a,variant:b,asChild:c=!1,...f}){let i=c?e.DX:"span";return(0,d.jsx)(i,{"data-slot":"badge",className:(0,g.cn)(h({variant:b}),a),...f})}},99891:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,472,753],()=>b(b.s=96666));module.exports=c})();